import { createContext, useContext, useState, useCallback } from 'react';
import { dispatchTeethClearedEvent } from '../utils/messageUtils';

// Create the context
const TeethContext = createContext();

// Custom hook to use the teeth context
export const useTeeth = () => {
  const context = useContext(TeethContext);
  if (!context) {
    throw new Error('useTeeth must be used within a TeethProvider');
  }
  return context;
};

export const TeethProvider = ({ children, initialTeeth = {} }) => {
  // Use initialTeeth as the initial state for patientTeeth
  const [patientTeeth, setPatientTeeth] = useState(initialTeeth);
  const [hoveredTooth, setHoveredTooth] = useState(null);
  const [selectedTooth, setSelectedTooth] = useState(null);
  const [hoveredMDTooth, setHoveredMDTooth] = useState(null);
  const [selectedMDTooth, setSelectedMDTooth] = useState(null);
  const [bridgeStart, setBridgeStart] = useState(null);
  const [selectedSurfaces, setSelectedSurfaces] = useState(null);
  const [selectedTreatment, setSelectedTreatment] = useState(null);
  // const [selectedTreatment, setSelectedTreatment] = useState(
  //   {
  //     Id: "111",
  //     ctid: "222",
  //     completed: false,
  //     name: "BoneGraft",
  //     full_tooth_treatment: true,
  //     patient_treatment: false,
  //     remove_tooth_when_completed: false,
  //     remove_treatment_when_completed: false,
  //     bridge_treatment: false,
  //     missing_tooth_indicator: false,
  //     mixed_dentition: false,
  //     created_at: "01-01-2025",
  //     completed_at: null
  //   },
  // );
  const [eraserToolActive, setEraserToolActive] = useState(false);
  const [treatmentVisibility, setTreatmentVisibility] = useState({});
  const [patientId, setPatientId] = useState(initialTeeth.patientId || null);

  // Toggle treatment visibility
  const toggleTreatmentVisibility = useCallback((toothNumber, treatmentId) => {
    // Debug log
    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
      console.log(`Toggling visibility for tooth ${toothNumber}, treatment ${treatmentId}`);
    }

    setTreatmentVisibility(prev => {
      const toothKey = `${toothNumber}`;
      const treatmentKey = `${treatmentId}`;
      const currentVisibility = prev[toothKey]?.[treatmentKey];
      const newVisibility = currentVisibility === undefined ? false : !currentVisibility;

      // Debug log
      if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
        console.log(`Current visibility: ${currentVisibility}, New visibility: ${newVisibility}`);
      }

      return {
        ...prev,
        [toothKey]: {
          ...(prev[toothKey] || {}),
          [treatmentKey]: newVisibility
        }
      };
    });
  }, []);

  // Get treatment visibility
  const getTreatmentVisibility = useCallback((toothNumber, treatmentId) => {
    const toothKey = `${toothNumber}`;
    const treatmentKey = `${treatmentId}`;
    const isVisible = treatmentVisibility[toothKey]?.[treatmentKey] !== false; // Default to visible (true) if not set

    // Debug log - but only occasionally to avoid flooding the console
    if (typeof window !== 'undefined' &&
        window.location.hostname === 'localhost' &&
        Math.random() < 0.01) { // Only log 1% of the time
      console.log(`Getting visibility for tooth ${toothNumber}, treatment ${treatmentId}: ${isVisible}`);
    }

    return isVisible;
  }, [treatmentVisibility]);
  const [missingToothActive, setMissingToothActive] = useState(null);
  const [resetTooth, setResetTooth] = useState(null);
  const [watchTooth, setWatchTooth] = useState(null);
  const [mixedDentation, setMixedDentation] = useState(null);

  // Add a treatment to a tooth
  const addTreatment = useCallback((toothNumber, treatment) => {
    setPatientTeeth(prevTeeth => {
      const tooth = prevTeeth[toothNumber] || {
        position: `UL${toothNumber}`,
        position_number: parseInt(toothNumber, 10),
        status: "healthy",
        lastTreatment: new Date().toISOString().split('T')[0],
        notes: "",
        marked_as_missing: false,
        marked_as_watched: false,
        treatments: []
      };

      // Ensure the treatment has all required fields
      const newTreatment = {
        Id: treatment.Id || treatment.id || Math.floor(Math.random() * 10000).toString(),
        ctid: treatment.ctid || treatment.ctId || "",
        completed: treatment.completed || false,
        name: treatment.name || "Default",
        full_tooth_treatment: treatment.full_tooth_treatment || false,
        patient_treatment: treatment.patient_treatment || false,
        remove_tooth_when_completed: treatment.remove_tooth_when_completed || false,
        remove_treatment_when_completed: treatment.remove_treatment_when_completed || false,
        bridge_treatment: treatment.bridge_treatment || false,
        missing_tooth_indicator: treatment.missing_tooth_indicator || false,
        mixed_dentition: treatment.mixed_dentition || false,
        created_at: treatment.created_at || new Date().toISOString(),
        completed_at: treatment.completed_at || null,
        surfaces: treatment.surfaces || {}
      };

      return {
        ...prevTeeth,
        [toothNumber]: {
          ...tooth,
          treatments: [...tooth.treatments, newTreatment]
        }
      };
    });
  }, []);

  // Remove the last treatment from a tooth
  const removeLastTreatment = useCallback((toothNumber) => {
    setPatientTeeth(prevTeeth => {
      const tooth = prevTeeth[toothNumber];
      if (!tooth || tooth.treatments.length === 0) return prevTeeth;

      return {
        ...prevTeeth,
        [toothNumber]: {
          ...tooth,
          treatments: tooth.treatments.slice(0, -1)
        }
      };
    });
  }, []);

  // Mark a tooth as missing
  const markToothAsMissing = useCallback((toothNumber, isMissing = true) => {
    setPatientTeeth(prevTeeth => {
      const tooth = prevTeeth[toothNumber] || {
        position: `UL${toothNumber}`,
        position_number: parseInt(toothNumber, 10),
        status: "healthy",
        lastTreatment: new Date().toISOString().split('T')[0],
        notes: "",
        treatments: []
      };

      // If marking as missing, add a missing tooth treatment
      const updatedTreatments = isMissing ? [] : tooth.treatments;

      // If marking as missing, we might want to add a missing tooth indicator treatment
      if (isMissing) {
        const missingTreatment = {
          Id: Math.floor(Math.random() * 10000).toString(),
          ctid: "",
          completed: true,
          name: "Missing",
          full_tooth_treatment: true,
          patient_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
          missing_tooth_indicator: true,
          mixed_dentition: false,
          created_at: new Date().toISOString(),
          completed_at: new Date().toISOString()
        };
        // We don't add the treatment here because marked_as_missing flag is enough
        // But we could add it if needed
      }

      return {
        ...prevTeeth,
        [toothNumber]: {
          ...tooth,
          marked_as_missing: isMissing,
          treatments: updatedTreatments
        }
      };
    });
  }, []);

  // Update tooth information
  const updateToothInfo = useCallback((toothNumber, info) => {
    setPatientTeeth(prevTeeth => {
      const tooth = prevTeeth[toothNumber] || {
        position: `UL${toothNumber}`,
        position_number: parseInt(toothNumber, 10),
        status: "healthy",
        lastTreatment: new Date().toISOString().split('T')[0],
        notes: "",
        marked_as_missing: false,
        marked_as_watched: false,
        treatments: []
      };

      // Process treatments if they're being updated
      let updatedInfo = { ...info };
      if (info.treatments) {
        // Ensure all treatments have the required fields
        updatedInfo.treatments = info.treatments.map(treatment => ({
          Id: treatment.Id || treatment.id || Math.floor(Math.random() * 10000).toString(),
          ctid: treatment.ctid || treatment.ctId || "",
          completed: treatment.completed || false,
          name: treatment.name || "Default",
          full_tooth_treatment: treatment.full_tooth_treatment || false,
          patient_treatment: treatment.patient_treatment || false,
          remove_tooth_when_completed: treatment.remove_tooth_when_completed || false,
          remove_treatment_when_completed: treatment.remove_treatment_when_completed || false,
          bridge_treatment: treatment.bridge_treatment || false,
          missing_tooth_indicator: treatment.missing_tooth_indicator || false,
          mixed_dentition: treatment.mixed_dentition || false,
          created_at: treatment.created_at || new Date().toISOString(),
          completed_at: treatment.completed_at || null,
          surfaces: treatment.surfaces || {}
        }));
      }

      return {
        ...prevTeeth,
        [toothNumber]: {
          ...tooth,
          ...updatedInfo
        }
      };
    });
  }, []);

  // Set all teeth to a specific treatment
  const setAllTeethTreatment = useCallback((treatmentName) => {
    setPatientTeeth(prevTeeth =>
      Object.fromEntries(
        Object.keys(prevTeeth).map(key => [
          key,
          {
            position: `UL${key}`,
            position_number: parseInt(key, 10),
            status: "healing",
            lastTreatment: new Date().toISOString().split('T')[0],
            notes: `${treatmentName} applied to all teeth`,
            marked_as_missing: false,
            marked_as_watched: false,
            treatments: [
              {
                Id: `id-${key}`,
                ctid: `ctid-${key}`,
                completed: false,
                name: treatmentName,
                full_tooth_treatment: true,
                patient_treatment: false,
                remove_tooth_when_completed: false,
                remove_treatment_when_completed: false,
                bridge_treatment: false,
                missing_tooth_indicator: false,
                mixed_dentition: false,
                created_at: new Date().toISOString(),
                completed_at: null,
                surfaces: {}
              },
            ]
          },
        ])
      )
    );
  }, []);

  // Get the treatment name for a tooth (for backward compatibility)
  const getToothTreatmentName = useCallback((toothNumber) => {
    const tooth = patientTeeth[toothNumber];
    if (!tooth || !tooth.treatments || tooth.treatments.length === 0) {
      return 'Default';
    }
    return tooth.treatments[0].name;
  }, [patientTeeth]);

  // Get the patient type (ADULT or CHILDREN)
  const getPatientType = useCallback(() => {
    return patientTeeth.patientType || "ADULT";
  }, [patientTeeth]);

  // Set the patient ID
  const setPatientIdentifier = useCallback((id) => {
    setPatientId(id);
    setPatientTeeth(prev => ({
      ...prev,
      patientId: id
    }));
  }, []);

  // Reset all treatment visibility (for debugging)
  const resetTreatmentVisibility = useCallback(() => {
    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
      console.log('Resetting all treatment visibility');
    }
    setTreatmentVisibility({});
  }, []);

  // Clear all teeth data and meshes
  const clearAllTeeth = useCallback(() => {
    // First, clear the state
    setPatientTeeth({});
    setSelectedTooth(null);
    setSelectedMDTooth(null);
    setSelectedSurfaces(null);
    setHoveredTooth(null);
    setHoveredMDTooth(null);

    // Dispatch a custom event to notify components that teeth should be cleared
    dispatchTeethClearedEvent();


  }, []);

  const value = {
    patientTeeth,
    setPatientTeeth,
    hoveredTooth,
    setHoveredTooth,
    selectedTooth,
    setSelectedTooth,
    hoveredMDTooth,
    setHoveredMDTooth,
    selectedMDTooth,
    setSelectedMDTooth,
    bridgeStart,
    setBridgeStart,
    selectedSurfaces,
    setSelectedSurfaces,
    selectedTreatment,
    setSelectedTreatment,
    eraserToolActive,
    setEraserToolActive,
    missingToothActive,
    setMissingToothActive,
    resetTooth,
    setResetTooth,
    watchTooth,
    setWatchTooth,
    mixedDentation,
    setMixedDentation,
    addTreatment,
    removeLastTreatment,
    markToothAsMissing,
    updateToothInfo,
    setAllTeethTreatment,
    getToothTreatmentName,
    getPatientType,
    clearAllTeeth,
    toggleTreatmentVisibility,
    getTreatmentVisibility,
    patientId,
    setPatientIdentifier,
    resetTreatmentVisibility
  };

  // Expose the context for debugging in development
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    // Create a debug-friendly version of the context
    window.__TEETH_CONTEXT__ = {
      ...value,
      treatmentVisibility: treatmentVisibility // Explicitly include the treatmentVisibility state
    };

    // Also expose a function to get the latest state
    window.__GET_TEETH_CONTEXT__ = () => ({
      ...value,
      treatmentVisibility: treatmentVisibility
    });
  }

  return (
    <TeethContext.Provider value={value}>
      {children}
    </TeethContext.Provider>
  );
};
