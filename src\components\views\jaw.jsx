import { useRef, useEffect, useState } from "react";
import { useThree, use<PERSON>rame } from "@react-three/fiber";
import { useGLTF } from "@react-three/drei";
import * as THREE from "three";
import { getModelUrl } from "../../constants/models"; // Import getModelUrl
import { createGumMaterial } from "../../constants/materials";
import { useTeeth } from "../../context/TeethContext";

export function Jaw({ pointersRef }) {
  const jawRef = useRef(null);
  const mixerRef = useRef(null);
  const actionsRef = useRef(new Map()); // Store actions for each animation
  const { scene: threeScene } = useThree();
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const frameCountRef = useRef(0); // Add a reference to track frame count
  const { getPatientType, getCurrentTreatmentName } = useTeeth(); // Assume getCurrentTreatmentName exists

  // Get the patient type (ADULT or CHILDREN)
  const patientType = getPatientType();
  // Get current treatment name, default to "Jaw" for generic jaw view
  const treatmentName = getCurrentTreatmentName
    ? getCurrentTreatmentName() || "Jaw"
    : "Jaw";

  const modelUrl = getModelUrl(treatmentName, "STATE_B", patientType);

  if (!modelUrl) {
    console.error(
      `[Jaw] Could not determine model URL for treatment: ${treatmentName}, patient: ${patientType}`,
    );
    // Potentially return null or an error component if modelUrl is null
    // For now, this will cause useGLTF to fail, which should be handled by an error boundary if present
  }

  // Load the model with DRACO compression based on patient type
  const { scene, animations } = useGLTF(modelUrl, {
    // Use the dynamic modelUrl
    draco: {
      decoderPath: "https://www.gstatic.com/draco/versioned/decoders/1.5.6/",
    },
  });

  // Helper function to recursively find and process pointers
  const findPointers = (object) => {
    if (object.name?.includes("_Pos") && pointersRef?.current) {
      const number = parseInt(object.name.split("_")[0], 10);
      if (!isNaN(number)) {
        pointersRef.current.set(number, object);
      }
    }

    object.children?.forEach((child) => findPointers(child));
  };

  // Setup animations
  const setupAnimations = () => {
    console.log("Setting up animations, count:", animations?.length || 0);

    // If there are no animations, we still create a mixer but don't set up actions
    const mixer = new THREE.AnimationMixer(scene);
    mixerRef.current = mixer;

    // Only set up animation actions if there are animations
    if (animations?.length) {
      mixer.addEventListener("finished", () => {
        setIsPlaying(false);
        actionsRef.current.forEach((action) => {
          action.setEffectiveWeight(0);
          action.paused = true;
        });
      });

      animations.forEach((clip) => {
        const action = mixer.clipAction(clip);
        actionsRef.current.set(clip.name, action);
        action.setLoop(THREE.LoopOnce);
        action.clampWhenFinished = true;
        action.setEffectiveWeight(0);
      });
    }
  };

  // Play or resume the animation
  const playAnimation = () => {
    // Reset the frame counter when starting or resuming
    if (!isPaused) {
      frameCountRef.current = 0;
    }

    if (isPaused) {
      actionsRef.current.forEach((action) => {
        action.paused = false;
        action.setEffectiveWeight(1);
      });
      setIsPaused(false);
    } else {
      actionsRef.current.forEach((action) => {
        action.reset().setEffectiveWeight(1).play();
      });
      setIsPlaying(true);
    }
  };

  // Pause the animation
  const pauseAnimation = () => {
    actionsRef.current.forEach((action) => {
      action.paused = true;
      // Don't set weight to 0 as it can cause visual reset
      // action.setEffectiveWeight(0);
    });

    // Don't update the mixer with 0 as it can reset the animation
    // if (mixerRef.current) {
    //   mixerRef.current.update(0);
    // }

    setIsPaused(true);
  };

  // Reset the animation to its initial state
  const resetAnimation = () => {
    frameCountRef.current = 0; // Reset frame counter

    actionsRef.current.forEach((action) => {
      action.stop();
      action.reset();
      action.setEffectiveWeight(0);
    });

    if (mixerRef.current) {
      mixerRef.current.update(0);
    }

    setIsPlaying(false);
    setIsPaused(false);
  };

  // Go to specific frame
  const goToFrame = (frameNumber) => {
    if (!mixerRef.current || actionsRef.current.size === 0) return;

    // First, reset the animation
    actionsRef.current.forEach((action) => {
      action.reset().setEffectiveWeight(1);
    });

    // Calculate the time to advance based on the frame number (assuming 30fps)
    const fps = 30;
    const timeToAdvance = frameNumber / fps;

    // Update the mixer to advance to that frame
    mixerRef.current.update(timeToAdvance);

    // Pause at that frame
    pauseAnimation();

    // Update frame counter
    frameCountRef.current = frameNumber;
  };

  // Expose animation controls globally
  if (typeof window !== "undefined") {
    window.jawAnimationControls = {
      play: playAnimation,
      pause: pauseAnimation,
      reset: resetAnimation,
      goToFrame: goToFrame, // Add the goToFrame function to global controls
      isPlaying: () => isPlaying,
      isPaused: () => isPaused,
      currentFrame: () => frameCountRef.current,
    };
  }

  // Animation update frame
  useFrame((_, delta) => {
    if (mixerRef.current && isPlaying && !isPaused) {
      // Always update the animation first
      mixerRef.current.update(delta);

      // Update the frame counter based on delta and a framerate assumption (e.g., 30 fps)
      const fps = 30;
      frameCountRef.current += delta * fps;

      // Check if we've reached frame 10
      if (frameCountRef.current >= 30 && frameCountRef.current < 31) {
        // Just pause without resetting
        setIsPaused(true);
        actionsRef.current.forEach((action) => {
          action.paused = true;
        });
        // console.log("Animation paused at frame 10");
      }
    }
  });

  useEffect(() => {
    console.log("Jaw - Scene loaded:", !!scene);
    console.log("Jaw - Animations:", animations);

    // Changed condition to only check for scene, as children's model might not have animations
    if (scene) {
      try {
        const container = new THREE.Group();

        // Only create the gum material since we're not using the skull material
        const gumMaterial = createGumMaterial();

        scene.traverse((child) => {
          if (child.isMesh) {
            if (child.name.includes("Gum")) {
              child.material = gumMaterial.clone();
            } else {
              // child.material = skullMaterial.clone();
            }
          }
        });

        setupAnimations();

        container.add(scene);
        if (patientType === "ADULT") {
          scene.position.set(-0.18, 0.04, 0);
          scene.rotation.set(0, 0, 0);
          scene.scale.set(1, 1, 1);
        }

        findPointers(scene);

        jawRef.current = container;
        threeScene.add(container);
      } catch (error) {
        console.error("Error setting up jaw model:", error);
      }
    }

    return () => {
      if (jawRef.current) {
        threeScene.remove(jawRef.current);
      }
      if (mixerRef.current) {
        mixerRef.current.stopAllAction();
      }
      actionsRef.current.clear();
    };
  }, [scene, threeScene, pointersRef, animations]);

  return null;
}

// Don't preload the model - we'll load it only when needed

export default Jaw;
