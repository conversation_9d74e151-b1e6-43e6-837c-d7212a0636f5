# Plan for Creating CODEBASE_INDEX.md for upod-v2

This document outlines the plan to create a comprehensive index of the `upod-v2` codebase, a React/Three.js dental visualization application.

## Phase 1: Information Gathering & Initial Analysis (Completed)

1.  **Project Overview & Purpose:**
    - Read `README.md` (Found to be a generic Vite template, so project purpose will be inferred from code).
    - Initial understanding: A dental visualization application.
2.  **Directory Structure & Key File Identification:**
    - Analyzed the provided file list from `environment_details`.
    - Identified key configuration files: `package.json`, `vite.config.js`, `eslint.config.js`.
    - Identified core application logic directories: `src/`, `src/components/`, `src/views/`, `src/hooks/`, `src/utils/`, `src/context/`, `src/apis/`, `src/constants/`.
3.  **Initial Scan of Key Files:**
    - Read `src/main.jsx`: Renders `<AppWrapper />`.
    - Read `src/AppWrapper.jsx`:
      - Sets up `TeethProvider` context.
      - Loads initial teeth data (sample data, differentiates between `single_treatment` view and regular views based on URL params).
      - Handles a loading state.
    - Read `src/App.jsx`:
      - Manages `currentView` state (skull, jaw, single_treatment, charting) based on URL params.
      - Uses `TeethContext` for `patientTeeth`, `hoveredTooth`, `selectedTooth`, etc.
      - Initializes `useTeethMessages` hook (likely for `postMessage` communication).
      - Initializes `screenshotMessageHandler`.
      - Contains logic for view toggling, animation controls (play/reset), and refs for different view pointers.
      - Renders the main `Scene` component along with UI elements like `ViewIndicator`, `ViewSwitcher`, and `AnimationControls`.
      - Passes down various state and handlers to the `Scene` component.
    - Read `package.json`:
      - Confirms React, Three.js, @react-three/fiber, @react-three/drei.
      - Build tool: Vite.
      - Linter: ESLint.
      - Scripts: `dev`, `build`, `lint`, `preview`.

## Phase 2: Detailed Analysis & Documentation (To Be Done in Code Mode)

This phase involves a deeper dive into the codebase to gather information for each section of the `CODEBASE_INDEX.md`.

**Structure of `CODEBASE_INDEX.md`:**

1.  **Project Overview and Purpose**

    - Describe the application's main goal (dental visualization).
    - Mention key features observed (different views: skull, jaw, charting, single tooth/treatment; interactions; animations).
    - Technology stack (React, Three.js, Vite).

2.  **Directory Structure**

    - List major directories (e.g., `public/`, `src/`) and their general purpose.
    - Detail subdirectories within `src/`:
      - `src/apis/`: Likely for external communication or `postMessage` definitions.
      - `src/assets/`: Static assets like images.
      - `src/components/`: Reusable UI and 3D components.
        - `src/components/UI/`: Specific UI elements.
        - `src/components/scene_control/`: Logic for camera, lights, interactions.
        - `src/components/scene_ui/`: UI elements related to the 3D scene.
        - `src/components/views/`: Components representing different application views (e.g., skull, jaw).
      - `src/constants/`: Application-wide constants (e.g., camera configs, materials, model paths, dictionaries).
      - `src/context/`: React context providers (e.g., `TeethContext`).
      - `src/helpers/`: Utility functions specific to certain features or views.
      - `src/hooks/`: Custom React hooks.
      - `src/utils/`: General utility functions.

3.  **Key Components and Their Responsibilities**

    - **Core Application Components:**
      - `src/main.jsx`: Entry point.
      - `src/AppWrapper.jsx`: Wraps `App` with `TeethProvider`, loads initial data (highlight sample data and `single_treatment` view logic).
      - `src/App.jsx`: Main application component, manages views, state, and renders `Scene` and UI controls. Handles `postMessage` setup.
      - `src/components/scene.jsx`: Core 3D rendering canvas and logic.
    - **View Components (`src/components/views/`)**:
      - `skull.jsx`, `jaw.jsx`, `charting.jsx`, `single_tooth.jsx`, `single_treatment_preview.jsx`, `single_treatment.jsx`: Detail the purpose of each view.
    - **UI Components (`src/components/UI/`)**:
      - `ViewIndicator.jsx`, `ViewSwitcher.jsx`, `AnimationControls.jsx`, `PatientIdInput.jsx`, `ScreenshotButton.jsx`, `ToggleSwitch.jsx`, `TreatmentsList.jsx`, etc.: Describe their function.
    - **3D Scene Components (`src/components/`)**:
      - `boxes.jsx` (if relevant), `charting_teeth.jsx`, `interactive_square.jsx`, `skull_jaw_teeth.jsx`: Describe their role in the 3D scene.
    - **Scene Control Components (`src/components/scene_control/`)**:
      - `camera.js`, `lights.js`, `mouse_interactions.js`: How they manage the 3D environment.

4.  **API Endpoints and Data Flow**

    - **Data Initialization:** How `initialTeethData` is loaded in `AppWrapper.jsx` (currently sample data, mention URL param `view=single_treatment` affecting this).
    - **`postMessage` Communication:**
      - Identify files involved: `src/apis/post_messages.js`, `src/hooks/useTeethMessages.js`, `src/utils/screenshotMessageHandler.js`, `src/utils/teethMessageHandler.js`.
      - Document the types of messages sent and received (e.g., `initialize_teeth`, `clear_teeth`, screenshot requests, treatment selections).
      - Explain how `window.dispatchEvent` and `window.addEventListener('message', ...)` are used.
    - **Internal Data Flow:** How data from `TeethContext` (e.g., `patientTeeth`, `selectedTooth`) is passed down and modified by components.

5.  **Utility Functions and Their Purposes (`src/helpers/`, `src/utils/`)**

    - Categorize and describe utilities:
      - `charting_helpers.js`, `global_helpers.js`, `single_view_helpers.js`, `skull_jaw_helpers.js`.
      - `cameraUtils.js`, `debugUtils.js`, `materialUtils.js`, `messageUtils.js`, `modelUtils.js`, `screenshotUtils.js`, `transparencyUtils.js`, `visualIndicatorUtils.js`.

6.  **Context Providers and State Management**

    - `src/context/TeethContext.jsx`:
      - Purpose: Manage global state related to teeth, patient data, UI interactions (hover, selection), and treatments.
      - Key state variables and setters provided by `useTeeth()`.
    - Local component state (`useState` in `App.jsx` and other components).

7.  **Constants and Configuration Files**

    - `src/constants/`:
      - `camera_config.js`: Default camera settings.
      - `dictionaries.js`: Mappings or lookup tables.
      - `materials.js`: Three.js material definitions.
      - `models.js`: Paths or configurations for 3D models.
    - Root configuration files: `vite.config.js`, `eslint.config.js`.

8.  **Testing Setup and Test Files**

    - Identify test files (e.g., `src/components/UI/ToggleSwitch.test.jsx`, `src/utils/screenshotUtils.test.js`).
    - Determine the testing framework/libraries used (likely Jest or Vitest, inferred from dependencies or test file syntax).
    - Describe the general approach to testing.

9.  **Relationships Between Different Parts of the Codebase**

    - **Component Hierarchy:**
      - Illustrate with a simplified tree or Mermaid diagram (e.g., `main.jsx` -> `AppWrapper` -> `TeethProvider` -> `App` -> `Scene` / UI controls -> View Components / 3D object components).
      ```mermaid
      graph TD
          A[main.jsx] --> B(AppWrapper.jsx);
          B --> C{TeethProvider};
          C --> D[App.jsx];
          D --> E(Scene.jsx);
          D --> F[UI Controls e.g., ViewSwitcher, AnimationControls];
          E --> G[View Components e.g., skull.jsx, jaw.jsx];
          G --> H[3D Object Components e.g., skull_jaw_teeth.jsx];
      end
      ```
    - **Data Flow Patterns:**

      - Context API (`TeethContext`) for global state.
      - Props drilling for localized state.
      - `postMessage` for external communication.
      - URL parameters for initial view state.

      ```mermaid
      sequenceDiagram
          participant ExternalApp
          participant AppWrapper
          participant App
          participant TeethContext
          participant SceneComponent

          ExternalApp->>AppWrapper: Sends initial data (via postMessage or URL param)
          AppWrapper->>TeethContext: Initializes with initialTeethData
          App->>TeethContext: Reads patientTeeth, selectedTooth
          App->>SceneComponent: Passes patientTeeth, view state
          SceneComponent->>TeethContext: Updates hoveredTooth, selectedTooth
          TeethContext-->>App: Notifies App of state changes
          App->>ExternalApp: Sends updates (via postMessage using useTeethMessages)
      end
      ```

    - **Dependencies Between Modules:**
      - Highlight key `import` relationships (e.g., views importing components, components importing hooks/utils).
    - **Integration Points:**
      - `postMessage` interface for embedding in other applications.
      - URL parameters (`?view=...`) for deep linking or setting initial state.

10. **Architectural Patterns, Design Decisions, or Notable Implementation Details**
    - **Component-Based Architecture** (React).
    - **Declarative 3D Scene** (@react-three/fiber).
    - **State Management:** Centralized via React Context (`TeethContext`), localized via `useState`.
    - **View Management:** URL parameter-driven and `useState` in `App.jsx`.
    - **Event-Driven Communication:** `window.postMessage` for external interactions.
    - **Use of Hooks:** Custom hooks like `useTeethMessages`, `useAnimationControls`, `useModelLoader` for encapsulating logic.
    - **Conditional Rendering:** Based on `currentView` and other state variables.
    - **Sample Data:** Currently uses extensive hardcoded sample data in `AppWrapper.jsx` for initialization.
    - **Global Animation Controls:** `window.jawAnimationControls` and `window.teethAnimationControls` suggest a global way to control animations, potentially for synchronization.

## Phase 3: Plan Review & Next Steps (Completed)

1.  Plan presented to the user.
2.  User approved the plan.
3.  This document (`CODEBASE_INDEX_PLAN.md`) will be created.
4.  Request switch to "code" mode to implement the indexing.

This plan will guide the creation of the `CODEBASE_INDEX.md` document.
