import { useRef, useEffect, useState, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useThree } from "@react-three/fiber";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import * as THREE from "three";
import { ensureAllMaterialsTransparent } from "../../utils/transparencyUtils";
import { applyTreatmentVisuals } from "../../utils/treatmentUtils"; // Added import
// Debug utils removed
import { useTeeth } from "../../context/TeethContext";
import { Html } from "@react-three/drei";
import TreatmentsListWrapper from "../UI/TreatmentsListWrapper";

// Import utility functions from modelUtils.js
import {
  modelCache,
  animationCache,
  createYPositionOnlyClip,
  getModelParts,
  cloneModel,
  // findExactSurfaceMorphTarget, // No longer used directly in this file
  forceSceneUpdate,
  clearAllTeethFromScene,
  checkForStateSpecificModel,
  getModelPathForPatientType,
} from "../../utils/modelUtils";

// findTreatmentIndex is no longer used directly in this file as visibility/ID logic is handled by TreatmentsListWrapper and applyTreatmentVisuals

export const SingleTreatment = () => {
  // Get teeth data from context
  const {
    patientTeeth,
    getPatientType,
    getTreatmentVisibility,
    toggleTreatmentVisibility,
    treatmentVisibility,
  } = useTeeth();

  // Get the patient type
  const patientType = getPatientType();

  // State and refs
  const [toothData, setToothData] = useState(null);
  const [isLoading, setIsLoading] = useState(true); // Used to track loading state
  const pointersRef = useRef(new Map());
  const loaderRef = useRef(new GLTFLoader());
  const mountedRef = useRef(true);
  const mixersRef = useRef(new Map());
  const actionsRef = useRef(new Map());
  const frameCountRef = useRef(0);
  const [isPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const toothRef = useRef(null);

  // Get access to the scene and renderer from React Three Fiber
  const { scene, gl: renderer, camera } = useThree();

  // Cleanup function for tooth models
  const cleanupTooth = useCallback(() => {
    // Cleanup mixers and actions
    mixersRef.current.forEach((mixer) => mixer.stopAllAction());
    mixersRef.current.clear();
    actionsRef.current.clear();

    // Clear the tooth ref
    toothRef.current = null;

    // Use the utility function to clear all teeth from the scene
    if (scene) {
      clearAllTeethFromScene(scene);
    }

    // Force a scene update
    if (renderer && scene && camera) {
      forceSceneUpdate(renderer, scene, camera);
    }
  }, [scene, renderer, camera]);

  useEffect(() => {
    mountedRef.current = true;
    loaderRef.current.setResourcePath(
      "https://upod.s3.eu-central-1.amazonaws.com/treatmentsV3/",
    );
    loaderRef.current.setCrossOrigin("anonymous");

    return () => {
      mountedRef.current = false;
      cleanupTooth();
    };
  }, [cleanupTooth]);

  const processAnimations = useCallback((animations, scene, treatmentName) => {
    if (!animations || animations.length === 0) return;

    // Create a mixer for the tooth
    let mixer = new THREE.AnimationMixer(scene);
    mixersRef.current.set(1, mixer); // Use 1 as the tooth number for single treatment

    animations.forEach((clip) => {
      const validTracks = clip.tracks.filter((track) => {
        const nodeName = track.name.split(".")[0];
        return scene.getObjectByName(nodeName);
      });

      if (validTracks.length === 0) return;

      const validClip = new THREE.AnimationClip(
        clip.name,
        clip.duration,
        validTracks,
      );

      // Create a unique key for this action
      const actionKey = `1_${treatmentName}_${validClip.name}`;

      // Create the action
      const action = mixer.clipAction(validClip);
      action.setLoop(THREE.LoopOnce);
      action.clampWhenFinished = true;

      actionsRef.current.set(actionKey, action);
    });
  }, []);

  const pauseAnimations = () => {
    actionsRef.current.forEach((action) => {
      action.paused = true;
    });
    setIsPaused(true);
  };

  useFrame((_, delta) => {
    if (isPlaying && !isPaused) {
      mixersRef.current.forEach((mixer) => mixer.update(delta));

      const fps = 30;
      frameCountRef.current += delta * fps;

      if (frameCountRef.current >= 30) {
        pauseAnimations();
      }
    }
  });

  const loadToothModel = useCallback(
    async (toothData, pointer) => {
      if (!mountedRef.current) {
        return;
      }

      if (!pointer || !pointer.isObject3D) {
        return;
      }

      const hasMissingToothTreatment =
        toothData.treatments &&
        toothData.treatments.some(
          (treatment) => treatment.missing_tooth_indicator,
        );

      if (toothData.marked_as_missing || hasMissingToothTreatment) {
        setIsLoading(false);
        return;
      }

      const positionMap = {
        UL8: 1,
        UL7: 2,
        UL6: 3,
        UL5: 4,
        UL4: 5,
        UL3: 6,
        UL2: 7,
        UL1: 8,
        UR1: 9,
        UR2: 10,
        UR3: 11,
        UR4: 12,
        UR5: 13,
        UR6: 14,
        UR7: 15,
        UR8: 16,
        LR8: 17,
        LR7: 18,
        LR6: 19,
        LR5: 20,
        LR4: 21,
        LR3: 22,
        LR2: 23,
        LR1: 24,
        LL1: 25,
        LL2: 26,
        LL3: 27,
        LL4: 28,
        LL5: 29,
        LL6: 30,
        LL7: 31,
        LL8: 32,
      };

      const number = positionMap[toothData.position] || 1;

      const existingParts = pointer.children.filter((c) =>
        c.name.startsWith(`tooth_${number}_`),
      );
      existingParts.forEach((part) => {
        pointer.remove(part);
        part.traverse((child) => {
          if (child.isMesh) {
            child.geometry?.dispose();
            if (Array.isArray(child.material)) {
              child.material.forEach((m) => m.dispose());
            } else {
              child.material?.dispose();
            }
          }
        });
      });

      // Sort treatments by creation date (newest first)
      const sortedTreatments = toothData.treatments
        ? [...toothData.treatments].sort((a, b) => {
            const dateA = new Date(a.created_at || 0);
            const dateB = new Date(b.created_at || 0);
            return dateB - dateA; // Newest first
          })
        : [];

      const modelParts = getModelParts(toothData);
      let primaryModel = null;

      // const decaySurfaces = // This is no longer used directly here
      //   sortedTreatments.length > 0 && sortedTreatments[0].surfaces
      //     ? Object.keys(sortedTreatments[0].surfaces)
      //     : [];

      for (const part of modelParts) {
        const hasStateSpecificModel = checkForStateSpecificModel(part);

        let modelPath;
        if (part === "Default") {
          modelPath = `Default/${number}.glb`;
        } else if (hasStateSpecificModel) {
          // Use A for single treatment view (like skull view)
          const stateSuffix = "_A";

          // Extract the base part name without any state suffix
          const basePartName = part.split("_")[0]; // Remove any existing state suffix

          // Construct the path with the correct state suffix
          modelPath = `${basePartName}${stateSuffix}/${number}_${basePartName}${stateSuffix}.glb`;
        } else {
          modelPath = `${part}/${number}_${part}.glb`;
        }

        try {
          let gltf;
          if (modelCache.has(modelPath)) {
            gltf = {
              scene: cloneModel(
                modelCache.get(modelPath).scene,
                "single_treatment",
              ),
            };
          } else {
            // Use the getModelPathForPatientType function to get the correct path
            const fullUrl = getModelPathForPatientType(
              "https://upod.s3.eu-central-1.amazonaws.com/treatmentsV3/",
              number,
              part,
              patientType,
            );

            gltf = await new Promise((resolve, reject) => {
              loaderRef.current.load(
                fullUrl,
                (result) => {
                  resolve(result);
                },
                undefined,
                (error) => {
                  reject(error);
                },
              );
            });
          }

          if (!modelCache.has(modelPath)) {
            modelCache.set(modelPath, {
              scene: cloneModel(gltf.scene, "single_treatment"),
            });

            if (gltf.animations?.length) {
              animationCache.set(
                modelPath,
                gltf.animations.map((a) => a.clone()),
              );
            }
          }

          // Get animations
          const animations = (animationCache.get(modelPath) || []).map((anim) =>
            createYPositionOnlyClip(anim),
          );

          if (!mountedRef.current) return;

          if (
            !gltf.scene ||
            !gltf.scene.children ||
            gltf.scene.children.length === 0
          ) {
            return;
          }

          const child = gltf.scene.children[0];
          child.position.set(0, 0, 0);
          child.rotation.set(0, 0, 0);
          child.name = `tooth_${number}_${part}_single_treatment`;

          child.userData = child.userData || {};
          child.userData.createdAt = Date.now();
          child.userData.viewType = "single_treatment";

          // Basic setup for the loaded part
          child.traverse((obj) => {
            if (obj.isMesh) {
              obj.userData = obj.userData || {};
              obj.userData.originalMaterial = obj.material.clone(); // Store original material
              obj.userData.number = parseInt(number, 10);
              obj.userData.type = "tooth";
              obj.userData.isInteractive = true; // single_treatment parts are interactive by default
              obj.userData.viewType = "single_treatment";
              obj.material.side = THREE.DoubleSide;
              obj.castShadow = true;
              obj.receiveShadow = true;
            }
          });

          // Set the primary model if this part's name matches the first treatment's name
          // or if it's the "Default" part and no primary model has been set yet.
          if (
            sortedTreatments.length > 0 &&
            part === sortedTreatments[0].name
          ) {
            primaryModel = child;
          } else if (part === "Default" && !primaryModel) {
            primaryModel = child;
          }

          // Ensure all materials in the model are transparent (original logic)
          ensureAllMaterialsTransparent(child, 0.8);

          if (number >= 9 && number <= 24) {
            child.scale.set(-child.scale.x, child.scale.y, -child.scale.z);
          }

          // Define treatmentName for rotation logic based on the primary (newest) treatment
          const treatmentName =
            sortedTreatments.length > 0 ? sortedTreatments[0].name : "Default";
          if (treatmentName === "Decay" || treatmentName === "Filling") {
            child.rotation.set(-89.5, 0, 0);
          }

          // For single treatment view, position the tooth exactly in the center
          // Perfect center position with no elevation
          child.position.set(0, 0, 0);

          // Scale the model to make it much more visible but maintain proportions
          const currentScale = child.scale.x;
          // Increase scale by 2.5x for an extreme close-up view
          child.scale.set(
            currentScale * 2.5,
            currentScale * 2.5,
            currentScale * 2.5,
          );

          // Log the final position, rotation and scale

          // Add the model to the pointer
          pointer.add(child);

          // If the pointer is not in the scene, add it back
          if (!pointer.parent && scene) {
            scene.add(pointer);
            pointer.userData = pointer.userData || {};
            pointer.userData.scene = scene;
          }

          if (renderer && scene && camera) {
            forceSceneUpdate(renderer, scene, camera);
          }

          if (animations.length) {
            processAnimations(animations, pointer, part); // Pass pointer as the scene for the mixer
          }
        } catch (err) {
          console.error(`Error loading tooth model part ${part}:`, err);
        }
      } // End of for (const part of modelParts)

      // After all parts for the tooth are loaded into the pointer (toothGroup)
      // Apply visual treatments
      if (pointer && toothData.treatments && toothData.treatments.length > 0) {
        const allTreatmentsOnToothSorted = [...toothData.treatments].sort(
          (a, b) => {
            const dateA = new Date(a.created_at || 0);
            const dateB = new Date(b.created_at || 0);
            return dateB - dateA; // Newest first
          },
        );

        for (const currentTreatment of allTreatmentsOnToothSorted) {
          applyTreatmentVisuals(
            pointer, // This is the toothGroup
            toothData,
            currentTreatment,
            allTreatmentsOnToothSorted,
            "single_treatment", // viewType
            getTreatmentVisibility,
          );
        }
      } else if (
        pointer &&
        (!toothData.treatments || toothData.treatments.length === 0)
      ) {
        // Handle the case for a default tooth with no specific treatments
        const defaultTreatmentData = {
          name: "Default",
          Id: `default_${number}`,
          created_at: new Date(0).toISOString(),
        };
        applyTreatmentVisuals(
          pointer,
          toothData,
          defaultTreatmentData,
          [defaultTreatmentData],
          "single_treatment",
          getTreatmentVisibility,
        );
      }

      // Store the primary model in the ref
      if (primaryModel) {
        toothRef.current = primaryModel;
      }

      setIsLoading(false);
    },
    [
      // Dependencies for loadToothModel
      processAnimations,
      renderer,
      scene,
      camera,
      getTreatmentVisibility,
      patientType,
      // applyTreatmentVisuals is not a dependency here as it's called within the same scope
    ],
  );

  // Initialize tooth data from patientTeeth
  useEffect(() => {
    if (patientTeeth && Object.keys(patientTeeth).length > 0) {
      const firstToothKey = Object.keys(patientTeeth)[0];
      const firstTooth = patientTeeth[firstToothKey];

      if (firstTooth) {
        setToothData(firstTooth);
      }
    } else {
      const defaultTooth = {
        position: "UL8",
        status: "status",
        lastTreatment: "01-01-2025",
        notes: "Default tooth for single treatment view",
        marked_as_missing: false,
        treatments: [
          {
            id: "111",
            ctid: "222",
            name: "BoneGraft",
            full_tooth_treatment: true,
            created_at: "01-01-2025",
          },
        ],
      };

      setToothData(defaultTooth);
    }
  }, [patientTeeth]); // Only depend on patientTeeth, not toothData

  useEffect(() => {
    const handleMessage = (event) => {
      if (event.data && event.data.type === "single_treatment_data") {
        setToothData(event.data.tooth);
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  useEffect(() => {
    if (!scene) {
      return;
    }

    const pointer = new THREE.Group();
    pointer.name = `tooth_pointer_1`;
    pointer.position.set(0, 0, 0);
    pointersRef.current.set(1, pointer);

    scene.add(pointer);

    // Store a reference to the scene to prevent garbage collection
    pointer.userData = pointer.userData || {};
    pointer.userData.scene = scene;
    pointer.userData.isMainPointer = true;

    const currentPointersRef = pointersRef.current;

    return () => {
      if (pointer.parent) {
        pointer.parent.remove(pointer);
      }
      currentPointersRef.clear();
    };
  }, [scene, camera, renderer]);

  useEffect(() => {
    if (toothData && pointersRef.current.size > 0) {
      const pointer = pointersRef.current.get(1);

      if (pointer) {
        if (!pointer.parent && scene) {
          scene.add(pointer);
        }

        cleanupTooth();
        loadToothModel(toothData, pointer);
      }
    }
  }, [toothData, cleanupTooth, loadToothModel, scene]);

  // Update model visibility when treatmentVisibility changes
  useEffect(() => {
    if (!toothRef.current || !toothData || !toothData.treatments) return;

    // Log that we're updating visibility
    console.log(
      "Updating model visibility based on treatmentVisibility changes",
    );

    // Update visibility for all treatments in the model
    toothRef.current.traverse((obj) => {
      if (obj.isMesh && obj.userData && obj.userData.treatmentId) {
        const treatmentId = obj.userData.treatmentId;
        const toothNumber = toothData.position_number || toothData.number;
        const isVisible = getTreatmentVisibility(toothNumber, treatmentId);

        // Only log if visibility is changing
        if (obj.visible !== isVisible) {
          console.log(
            `Updating visibility for ${obj.name} (${treatmentId}): ${isVisible}`,
          );
          obj.visible = isVisible;
        }
      }
    });


    const loadToothModel = useCallback(async (toothData, pointer) => {



        if (!mountedRef.current) {

            return;
        }


        if (!pointer || !pointer.isObject3D) {

            return;
        }


        const hasMissingToothTreatment = toothData.treatments &&
            toothData.treatments.some(treatment => treatment.missing_tooth_indicator);

        if (toothData.marked_as_missing || hasMissingToothTreatment) {
            setIsLoading(false);
            return;
        }




        const positionMap = {
            "UL8": 1, "UL7": 2, "UL6": 3, "UL5": 4, "UL4": 5, "UL3": 6, "UL2": 7, "UL1": 8,
            "UR1": 9, "UR2": 10, "UR3": 11, "UR4": 12, "UR5": 13, "UR6": 14, "UR7": 15, "UR8": 16,
            "LR8": 17, "LR7": 18, "LR6": 19, "LR5": 20, "LR4": 21, "LR3": 22, "LR2": 23, "LR1": 24,
            "LL1": 25, "LL2": 26, "LL3": 27, "LL4": 28, "LL5": 29, "LL6": 30, "LL7": 31, "LL8": 32
        };

        const number = positionMap[toothData.position] || 1;


        const existingParts = pointer.children.filter(c => c.name.startsWith(`tooth_${number}_`));
        existingParts.forEach(part => {
            pointer.remove(part);
            part.traverse(child => {
                if (child.isMesh) {
                    child.geometry?.dispose();
                    if (Array.isArray(child.material)) {
                        child.material.forEach(m => m.dispose());
                    } else {
                        child.material?.dispose();
                    }
                }
            });
        });

        // Sort treatments by creation date (newest first)
        const sortedTreatments = toothData.treatments
            ? [...toothData.treatments].sort((a, b) => {
                const dateA = new Date(a.created_at || 0);
                const dateB = new Date(b.created_at || 0);
                return dateB - dateA; // Newest first
            })
            : [];


        const modelParts = getModelParts(toothData);
        let primaryModel = null;


        const decaySurfaces = sortedTreatments.length > 0 && sortedTreatments[0].surfaces
            ? Object.keys(sortedTreatments[0].surfaces)
            : [];


        for (const part of modelParts) {
            const hasStateSpecificModel = checkForStateSpecificModel(part);


            let modelPath;
            if (part === "Default") {
                modelPath = `Default/${number}.glb`;
            } else if (hasStateSpecificModel) {
                // Use A for single treatment view (like skull view)
                const stateSuffix = "_A";

                // Extract the base part name without any state suffix
                const basePartName = part.split('_')[0]; // Remove any existing state suffix

                // Construct the path with the correct state suffix
                modelPath = `${basePartName}${stateSuffix}/${number}_${basePartName}${stateSuffix}.glb`;
            } else {
                modelPath = `${part}/${number}_${part}.glb`;
            }



            try {
                let gltf;
                if (modelCache.has(modelPath)) {
                    gltf = { scene: cloneModel(modelCache.get(modelPath).scene, "single_treatment") };
                } else {
                    // Use the getModelPathForPatientType function to get the correct path
                    const fullUrl = getModelPathForPatientType(
                        "https://upod.s3.eu-central-1.amazonaws.com/treatmentsV3/",
                        number,
                        part,
                        patientType
                    );

                    gltf = await new Promise((resolve, reject) => {
                        loaderRef.current.load(
                            fullUrl,
                            (result) => {
                                resolve(result);
                            },
                            undefined,
                            (error) => {

                                reject(error);
                            }
                        );
                    });
                }


                if (!modelCache.has(modelPath)) {
                    modelCache.set(modelPath, {
                        scene: cloneModel(gltf.scene, "single_treatment")
                    });

                    if (gltf.animations?.length) {
                        animationCache.set(
                            modelPath,
                            gltf.animations.map(a => a.clone())
                        );
                    }
                }

                // Get animations
                const animations = (animationCache.get(modelPath) || []).map(
                    anim => createYPositionOnlyClip(anim)
                );

                if (!mountedRef.current) return;

                if (!gltf.scene || !gltf.scene.children || gltf.scene.children.length === 0) {
                    return;
                }

                const child = gltf.scene.children[0];
                child.position.set(0, 0, 0);
                child.rotation.set(0, 0, 0);
                child.name = `tooth_${number}_${part}_single_treatment`;

                child.userData = child.userData || {};
                child.userData.createdAt = Date.now();
                child.userData.viewType = "single_treatment";

                // Collect all meshes
                const allMeshes = [];
                child.traverse(obj => {
                    if (obj.isMesh) {
                        allMeshes.push(obj);
                    }
                });

                // Setup meshes and apply morph targets
                child.traverse(obj => {
                    if (obj.isMesh) {
                        obj.userData = obj.userData || {};
                        obj.userData.originalMaterial = obj.material.clone();
                        obj.userData.number = number;
                        obj.userData.type = "tooth";
                        obj.userData.isInteractive = true;
                        obj.userData.viewType = "single_treatment";

                        // Default yellow color for all filling objects
                        if (part === "Filling") {
                            // Clone the original material
                            const fillingMaterial = obj.material.clone();

                            // Set basic properties
                            fillingMaterial.color.set(0xffff00);
                            fillingMaterial.emissive.set(0x333300);
                            fillingMaterial.transparent = true;
                            fillingMaterial.opacity = 0.8; // 80% opacity as requested

                            // Apply anti-z-fighting properties using our utility function
                            // This ensures consistent transparency handling
                            obj.material = applyAntiZFightingProperties(
                                fillingMaterial,
                                0, // Index 0 for default treatment
                                true // Make it transparent
                            );

                            obj.visible = true;
                        }

                        // Apply transparency for all treatments
                        if (toothData.treatments && toothData.treatments.length > 0) {
                            // Find the treatment that corresponds to this part
                            const treatmentForPart = sortedTreatments.find(t => t.name === part);

                            // If we have multiple treatments and this part corresponds to one of them
                            if (sortedTreatments.length > 1 && treatmentForPart) {
                                // Find the index of this treatment in the sorted array
                                const index = sortedTreatments.indexOf(treatmentForPart);

                                // Store the treatment information in the object's userData
                                obj.userData.treatmentIndex = index;
                                obj.userData.treatmentName = treatmentForPart.name;
                                obj.userData.treatmentId = treatmentForPart.Id || treatmentForPart.id;

                                // Apply anti-z-fighting properties to the material
                                obj.material = applyAntiZFightingProperties(
                                    obj.material,
                                    index,
                                    true // Make it transparent
                                );

                                // We're not modifying positions anymore, only transparency
                                // This keeps the treatments in their original positions

                                // Set render order based on index (higher index = rendered first)
                                obj.renderOrder = -index; // Negative so newer treatments render later

                                // Check if this treatment should be visible
                                // Use a consistent ID for the treatment
                                // Create a unique ID by combining treatment ID and index
                                const baseId = treatmentForPart.Id || treatmentForPart.id || `treatment_${index}`;

                                // Find the index of this treatment in the toothData.treatments array
                                // Use the part name and mesh name to match with the treatment name
                                const treatmentIndex = findTreatmentIndex(
                                    toothData.treatments,
                                    treatmentForPart,
                                    part,
                                    'Main treatment -',
                                    obj.name // Pass the mesh name to help with matching
                                );

                                // Special case for specific treatments
                                let actualIndex = treatmentIndex >= 0 ? treatmentIndex : index;

                                // Check if this is a specific treatment that needs special handling
                                if (obj.name.includes('Externalsinuslift')) {
                                    // Find the Externalsinuslift treatment in the toothData.treatments array
                                    const externalIndex = toothData.treatments.findIndex(t =>
                                        t.name.toLowerCase() === 'externalsinuslift'
                                    );
                                    if (externalIndex >= 0) {
                                        actualIndex = externalIndex;
                                        console.log(`Special case: Using index ${actualIndex} for Externalsinuslift`);
                                    }
                                } else if (obj.name.includes('RetaineRoot')) {
                                    // Find the RetaineRoot treatment in the toothData.treatments array
                                    const retainedIndex = toothData.treatments.findIndex(t =>
                                        t.name.toLowerCase() === 'retaineroot'
                                    );
                                    if (retainedIndex >= 0) {
                                        actualIndex = retainedIndex;
                                        console.log(`Special case: Using index ${actualIndex} for RetaineRoot`);
                                    }
                                }

                                const treatmentId = `${baseId}_${actualIndex}`;

                                obj.userData.treatmentId = treatmentId; // Store for future reference
                                obj.userData.baseId = baseId; // Store the original ID for reference
                                obj.userData.treatmentIndex = actualIndex; // Store the index for reference
                                obj.userData.treatmentName = treatmentForPart.name; // Store the name for reference

                                const isVisible = getTreatmentVisibility(toothData.position_number || toothData.number, treatmentId);
                                obj.visible = isVisible;

                                console.log(`Set visibility for ${obj.name} (${treatmentId}) to ${isVisible}. Treatment index: ${actualIndex}`);
                            }
                        }

                        // Apply morph targets for decay and filling parts
                        if (
                            obj.morphTargetInfluences &&
                            obj.morphTargetDictionary &&
                            toothData.treatments &&
                            toothData.treatments.length > 0 &&
                            (part === "Decay" || part === "Filling")
                        ) {
                            const morphTargetNames = Object.keys(obj.morphTargetDictionary);

                            // Process each treatment
                            sortedTreatments.forEach(treatment => {
                                // Skip treatments without surfaces
                                if (!treatment.surfaces) return;

                                // Process each surface in the treatment
                                Object.entries(treatment.surfaces).forEach(([surfaceName, surfaceData]) => {
                                    if (surfaceData.decaySeverity !== undefined) {
                                        // Find the exact morph target for this surface
                                        const morphTarget = findExactSurfaceMorphTarget(
                                            morphTargetNames,
                                            surfaceName,
                                            part
                                        );

                                        if (morphTarget) {
                                            const targetIndex = obj.morphTargetDictionary[morphTarget];
                                            obj.morphTargetInfluences[targetIndex] = surfaceData.decaySeverity;

                                            // For filling parts, check visibility and apply anti-z-fighting
                                            if (part === "Filling") {
                                                // Check if this treatment should be visible
                                                // Use a consistent ID for the treatment
                                                // Create a unique ID by combining treatment ID and index
                                                const sortedIndex = sortedTreatments.indexOf(treatment);
                                                const baseId = treatment.Id || treatment.id || `treatment_${sortedIndex}`;

                                                // Find the index of this treatment in the toothData.treatments array
                                                // Use the part name and mesh name to match with the treatment name
                                                const treatmentIndex = findTreatmentIndex(
                                                    toothData.treatments,
                                                    treatment,
                                                    part,
                                                    'Morph target -',
                                                    obj.name // Pass the mesh name to help with matching
                                                );

                                                // Use the same index as in the UI
                                                const actualIndex = treatmentIndex >= 0 ? treatmentIndex : sortedIndex;
                                                const treatmentId = `${baseId}_${actualIndex}`;

                                                obj.userData.treatmentId = treatmentId; // Store for future reference
                                                obj.userData.baseId = baseId; // Store the original ID for reference
                                                obj.userData.treatmentIndex = actualIndex; // Store the index for reference
                                                obj.userData.treatmentName = treatment.name; // Store the name for reference

                                                const isVisible = getTreatmentVisibility(toothData.position_number || toothData.number, treatmentId);
                                                obj.visible = isVisible;

                                                console.log(`Set visibility for morph target ${obj.name} (${treatmentId}) to ${isVisible}. Treatment index: ${actualIndex}`);

                                                // We already have the index from above

                                                // Always apply anti-z-fighting properties for consistent transparency
                                                obj.material = applyAntiZFightingProperties(
                                                    obj.material,
                                                    actualIndex,
                                                    true // Make it transparent
                                                );

                                                // We're not modifying positions anymore, only transparency
                                                // This keeps the treatments in their original positions

                                                // Set render order
                                                obj.renderOrder = -actualIndex;
                                            }
                                        }
                                    }
                                });
                            });
                        }

                        obj.material.side = THREE.DoubleSide;
                        obj.castShadow = true;
                        obj.receiveShadow = true;
                    }
                });

                // Apply material coloring for Decay
                if (
                    part === "Decay" &&
                    toothData.treatments &&
                    toothData.treatments.length > 0
                ) {
                    // Process each treatment that has surfaces
                    sortedTreatments.forEach((treatment, treatmentIndex) => {
                        if (!treatment.surfaces) return;

                        Object.entries(treatment.surfaces).forEach(([surfaceName, surfaceData]) => {
                            if (surfaceData.decaySeverity > 0) {
                                // The specific material name we want to match exactly
                                const targetMaterialName = `decay_${surfaceName.toLowerCase()}`;

                                // Look through all meshes for exact match
                                allMeshes.forEach(mesh => {
                                    const materialName = (mesh.material?.name || "").toLowerCase();

                                    // Only color if the material name is EXACTLY "decay_[surfaceName]"
                                    if (materialName === targetMaterialName) {
                                        // Clone the material to avoid affecting other meshes
                                        if (!mesh.userData.originalMaterial) {
                                            mesh.userData.originalMaterial = mesh.material.clone();
                                        }

                                        // Create a new material with anti-z-fighting properties
                                        const decayMaterial = mesh.userData.originalMaterial.clone();
                                        decayMaterial.color.set(0x000000);

                                        // Apply anti-z-fighting properties with transparency
                                        mesh.material = applyAntiZFightingProperties(
                                            decayMaterial,
                                            treatmentIndex,
                                            true // Make it transparent
                                        );

                                        // We're not modifying positions anymore, only transparency
                                        // This keeps the treatments in their original positions

                                        // Check if this treatment should be visible
                                        // Use a consistent ID for the treatment
                                        // Create a unique ID by combining treatment ID and index
                                        const baseId = treatment.Id || treatment.id || `treatment_${treatmentIndex}`;

                                        // Find the index of this treatment in the toothData.treatments array
                                        // Use the treatment name and mesh name to match with the treatment name
                                        const actualTreatmentIndex = findTreatmentIndex(
                                            toothData.treatments,
                                            treatment,
                                            'Decay',
                                            'Decay material -',
                                            mesh.name // Pass the mesh name to help with matching
                                        );

                                        // Use the same index as in the UI
                                        const actualIndex = actualTreatmentIndex >= 0 ? actualTreatmentIndex : treatmentIndex;
                                        const treatmentId = `${baseId}_${actualIndex}`;

                                        mesh.userData.treatmentId = treatmentId; // Store for future reference
                                        mesh.userData.baseId = baseId; // Store the original ID for reference
                                        mesh.userData.treatmentIndex = actualIndex; // Store the index for reference
                                        mesh.userData.treatmentName = treatment.name; // Store the name for reference

                                        const isVisible = getTreatmentVisibility(
                                            toothData.position_number || toothData.number,
                                            treatmentId
                                        );
                                        mesh.visible = isVisible;

                                        console.log(`Set visibility for decay mesh ${mesh.name} (${treatmentId}) to ${isVisible}. Treatment index: ${actualIndex}`);
                                    }
                                });
                            }
                        });
                    });
                }

                // Apply material coloring for Filling
                if (part === "Filling" && decaySurfaces.length > 0) {
                    decaySurfaces.forEach(surfaceName => {
                        const surfaceData = toothData.treatments[0].surfaces[surfaceName];
                        if (surfaceData && surfaceData.decaySeverity > 0) {
                            // The corresponding filling material would be named similarly
                            const targetMaterialName = `filling_${surfaceName.toLowerCase()}`;

                            // Look through all meshes for exact match
                            allMeshes.forEach(mesh => {
                                const materialName = (mesh.material?.name || "").toLowerCase();

                                // Only apply filling color if the material name is EXACTLY "filling_[surfaceName]"
                                if (materialName === targetMaterialName) {
                                    // Clone the material to avoid affecting other meshes
                                    if (!mesh.userData.originalMaterial) {
                                        mesh.userData.originalMaterial = mesh.material.clone();
                                    }

                                    // Create a new material with anti-z-fighting properties
                                    const fillingMaterial = mesh.userData.originalMaterial.clone();
                                    fillingMaterial.color.set(0xffff00);
                                    fillingMaterial.emissive.set(0x333300);

                                    // Apply anti-z-fighting properties with transparency
                                    mesh.material = applyAntiZFightingProperties(
                                        fillingMaterial,
                                        0, // Index 0 for the newest treatment
                                        true // Make it transparent
                                    );

                                    // Check if this treatment should be visible
                                    // Use a consistent ID for the treatment
                                    // Create a unique ID by combining treatment ID and index
                                    const treatment = toothData.treatments[0];
                                    const baseId = treatment.Id || treatment.id || 'treatment_0';

                                    // Find the index of this treatment in the toothData.treatments array
                                    const treatmentIndex = 0; // It's the first treatment
                                    const treatmentId = `${baseId}_${treatmentIndex}`;

                                    mesh.userData.treatmentId = treatmentId; // Store for future reference
                                    mesh.userData.baseId = baseId; // Store the original ID for reference
                                    mesh.userData.treatmentIndex = treatmentIndex; // Store the index for reference
                                    mesh.userData.treatmentName = treatment.name; // Store the name for reference

                                    const isVisible = getTreatmentVisibility(
                                        toothData.position_number || toothData.number,
                                        treatmentId
                                    );
                                    mesh.visible = isVisible;

                                    console.log(`Set visibility for filling mesh ${mesh.name} (${treatmentId}) to ${isVisible}. Treatment index: ${treatmentIndex}`);
                                }
                            });
                        }
                    });
                }

                // Get the treatment name
                const treatmentName = toothData.treatments && toothData.treatments.length > 0
                    ? toothData.treatments[0].name
                    : "Default";

                // Set the primary model based on the treatment name
                if (part === treatmentName) {
                    primaryModel = child;
                }

                // Ensure all materials in the model are transparent
                ensureAllMaterialsTransparent(child, 0.8);

                if (number >= 9 && number <= 24) {
                    child.scale.set(-child.scale.x, child.scale.y, -child.scale.z);
                }

                if (treatmentName === "Decay" || treatmentName === "Filling") {
                    child.rotation.set(-89.5, 0, 0);
                }

                // For single treatment view, position the tooth exactly in the center
                // Perfect center position with no elevation
                child.position.set(0, 0, 0);

                // Scale the model to make it much more visible but maintain proportions
                const currentScale = child.scale.x;
                // Increase scale by 2.5x for an extreme close-up view
                child.scale.set(currentScale * 2.5, currentScale * 2.5, currentScale * 2.5);

                // Log the final position, rotation and scale


                // Add the model to the pointer
                pointer.add(child);


                // If the pointer is not in the scene, add it back
                if (!pointer.parent && scene) {

                    scene.add(pointer);


                    pointer.userData = pointer.userData || {};
                    pointer.userData.scene = scene;
                }


                if (renderer && scene && camera) {

                    forceSceneUpdate(renderer, scene, camera);
                }


                if (animations.length) {
                    processAnimations(animations, pointer, part);
                }
            } catch (err) {
                // Error loading tooth
                console.error("Error loading tooth model:", err);
            }
        }

        // Store the primary model in the ref
        if (primaryModel) {
            toothRef.current = primaryModel;
        }

        setIsLoading(false);
    }, [processAnimations, renderer, scene, camera, getTreatmentVisibility, patientType]);

    // Initialize tooth data from patientTeeth
    useEffect(() => {


        if (patientTeeth && Object.keys(patientTeeth).length > 0) {

            const firstToothKey = Object.keys(patientTeeth)[0];
            const firstTooth = patientTeeth[firstToothKey];



            if (firstTooth) {

                setToothData(firstTooth);
            }
        } else {


            const defaultTooth = {
                position: "UL8",
                status: "status",
                lastTreatment: "01-01-2025",
                notes: "Default tooth for single treatment view",
                marked_as_missing: false,
                marked_as_watched: false,
                treatments: [
                    {
                        id: "111",
                        ctid: "222",
                        name: "BoneGraft",
                        full_tooth_treatment: true,
                        created_at: "01-01-2025"
                    }
                ]
            };

            setToothData(defaultTooth);
        }
    }, [patientTeeth]); // Only depend on patientTeeth, not toothData


    useEffect(() => {


        const handleMessage = (event) => {
            if (event.data && event.data.type === 'single_treatment_data') {

                setToothData(event.data.tooth);
            }
        };

        window.addEventListener('message', handleMessage);

        return () => {
            window.removeEventListener('message', handleMessage);
        };
    }, []);


    useEffect(() => {
        if (!scene) {
            return;
        }


        const pointer = new THREE.Group();
        pointer.name = `tooth_pointer_1`;
        pointer.position.set(0, 0, 0);
        pointersRef.current.set(1, pointer);


        scene.add(pointer);


        // Store a reference to the scene to prevent garbage collection
        pointer.userData = pointer.userData || {};
        pointer.userData.scene = scene;
        pointer.userData.isMainPointer = true;




        const currentPointersRef = pointersRef.current;

        return () => {


            if (pointer.parent) {
                pointer.parent.remove(pointer);
            }
            currentPointersRef.clear();
        };
    }, [scene, camera, renderer]);


    useEffect(() => {
        if (toothData && pointersRef.current.size > 0) {
            const pointer = pointersRef.current.get(1);

            if (pointer) {
                if (!pointer.parent && scene) {
                    scene.add(pointer);
                }

                cleanupTooth();
                loadToothModel(toothData, pointer);
            }
        }
    }, [toothData, cleanupTooth, loadToothModel, scene]);

    // Update model visibility when treatmentVisibility changes
    useEffect(() => {
        if (!toothRef.current || !toothData || !toothData.treatments) return;

        // Log that we're updating visibility
        console.log('Updating model visibility based on treatmentVisibility changes');

        // Update visibility for all treatments in the model
        toothRef.current.traverse(obj => {
            if (obj.isMesh && obj.userData && obj.userData.treatmentId) {
                const treatmentId = obj.userData.treatmentId;
                const toothNumber = toothData.position_number || toothData.number;
                const isVisible = getTreatmentVisibility(toothNumber, treatmentId);

                // Only log if visibility is changing
                if (obj.visible !== isVisible) {
                    console.log(`Updating visibility for ${obj.name} (${treatmentId}): ${isVisible}`);
                    obj.visible = isVisible;
                }
            }
        });

        // Log the current visibility state for debugging
        console.log('Current visibility state:');
        toothData.treatments.forEach((treatment, index) => {
            const baseId = treatment.Id || treatment.id || `treatment_${index}`;
            const treatmentId = `${baseId}_${index}`;
            const toothNumber = toothData.position_number || toothData.number;
            const isVisible = getTreatmentVisibility(toothNumber, treatmentId);
            console.log(`Treatment ${treatment.name} (${treatmentId}) on tooth ${toothNumber}: ${isVisible ? 'Visible' : 'Hidden'}`);
        });

        // Force a render to update the scene
        if (renderer && scene && camera) {
            forceSceneUpdate(renderer, scene, camera);
        }
    }, [treatmentVisibility, toothData, getTreatmentVisibility, renderer, scene, camera]);

    // Ensure camera is set up for rotation
    useEffect(() => {
        if (!camera || !renderer) return;

        // Set camera position for a good view of the tooth
        camera.position.set(0, 0, 0.4);
        camera.lookAt(0, 0, 0);
        camera.updateProjectionMatrix();

        // Force a render to update the scene
        if (renderer && scene) {
            renderer.render(scene, camera);
        }

        console.log('Camera position set for single treatment view');
    }, [camera, renderer, scene]);

    return (
        <group>
            {isLoading && (
                <Html position={[0, 0, 0]} center>
                    <div style={{
                        background: 'rgba(255, 255, 255, 0.8)',
                        padding: '10px 20px',
                        borderRadius: '8px',
                        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
                        fontFamily: 'Arial, sans-serif'
                    }}>
                        Loading tooth model...
                    </div>
                </Html>
            )}
            {toothData && (
                <Html fullscreen zIndexRange={[10, 0]} style={{ pointerEvents: 'none' }}>
                    <div style={{ pointerEvents: 'auto' }}>
                        <TreatmentsListWrapper
                            toothData={toothData}
                            onToggleVisibility={toggleTreatmentVisibility}
                            getVisibility={getTreatmentVisibility}
                        />
                    </div>
                </Html>
            )}
        </group>
    );
};

export default SingleTreatment;
