import { useRef, useEffect } from "react";
import { useThree } from "@react-three/fiber";
import { useGLTF } from "@react-three/drei";
import { DRACOLoader } from "three/examples/jsm/loaders/DRACOLoader";
import { MODELS } from "../../constants/models";
import {
  createSkullMaterial,
  createGumMaterial,
} from "../../constants/materials";
import { useTeeth } from "../../context/TeethContext";

export function Skull({ pointersRef }) {
  const skullRef = useRef(null);
  const { scene: threeScene } = useThree();
  const { getPatientType } = useTeeth();

  // Get the patient type (ADULT or CHILDREN)
  const patientType = getPatientType();

  // Load the model with DRACO compression based on patient type
  const { scene } = useGLTF(MODELS.SKULL[patientType].STATE_A, {
    draco: {
      decoderPath: "https://www.gstatic.com/draco/versioned/decoders/1.5.6/",
    },
  });

  // Helper function to recursively find and process pointers
  const findPointers = (object) => {
    if (object.name?.includes("_Pointer") && pointersRef?.current) {
      const number = parseInt(object.name.split("_")[0], 10);
      if (!isNaN(number)) {
        pointersRef.current.set(number, object);
      }
    }

    // Recursively process children
    if (object.children && object.children.length > 0) {
      object.children.forEach((child) => findPointers(child));
    }
  };

  useEffect(() => {
    if (scene) {
      try {
        // Create materials once
        const skullMaterial = createSkullMaterial();
        const gumMaterial = createGumMaterial();

        // Process materials
        scene.traverse((child) => {
          if (child.isMesh) {
            // Apply appropriate material based on mesh name
            if (
              child.name === "StateALowerGum" ||
              child.name === "StateAUpperGum" || child.name === "ChildStateALowerGum" || child.name === "ChildStateAUpperGum"
            ) {
              child.material = gumMaterial.clone();
            } else if (child.name === "Skeletal_Cranium" || child.name === "Skeletal_Mandible" || child.name === "ChildSkeletal_Cranium" || child.name === "ChildSkeletal_Mandible") {

              child.material = skullMaterial.clone();
            }
          }
        });

        // Set initial position and rotation
        scene.position.set(0, 0, 0);
        scene.rotation.set(0, 0, 0);
        scene.scale.set(1, 1, 1);

        // Find all pointers in the scene hierarchy
        findPointers(scene);

        // Add to scene
        skullRef.current = scene;
        threeScene.add(scene);
      } catch (error) {
        console.error("Error setting up skull model:", error);
      }
    }

    return () => {
      if (skullRef.current) {
        threeScene.remove(skullRef.current);
      }
    };
  }, [scene, threeScene, pointersRef]);

  return null;
}

// Don't preload the model - we'll load it only when needed

export default Skull;