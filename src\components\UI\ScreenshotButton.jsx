import React, { useState } from 'react';
import { useTeeth } from '../../context/TeethContext';
import { takeScreenshot, takeFixedSizeScreenshot, uploadScreenshot } from '../../utils/screenshotUtils';
import './ScreenshotButton.css';

/**
 * Button component for taking and uploading screenshots
 * @param {Object} props - Component props
 * @param {Object} props.renderer - The Three.js renderer
 * @param {Object} props.camera - The Three.js camera
 * @param {Object} props.controls - The OrbitControls instance
 * @param {string} props.currentView - The current view mode
 */
const ScreenshotButton = ({ renderer, camera, controls, currentView }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState(null);
  const { patientId } = useTeeth();

  const handleScreenshot = async () => {
    if (!renderer || isUploading) return;

    try {
      setIsUploading(true);
      setUploadStatus({ type: 'info', message: 'Taking screenshot...' });

      // Use the fixed-size screenshot function if all required props are available
      let blob;
      if (renderer && camera && controls && currentView) {
        console.log(`Taking fixed-size screenshot in ${currentView} view...`);
        blob = await takeFixedSizeScreenshot(
          renderer,
          camera,
          { current: controls },
          renderer.scene,
          currentView
        );
      } else {
        // Fallback to the original method
        console.log('Falling back to standard screenshot method...');

        // Force a render before taking the screenshot
        if (renderer.scene && renderer.camera) {
          renderer.render(renderer.scene, renderer.camera);
        }

        // Take the screenshot with the standard method
        blob = await takeScreenshot(renderer);
      }

      setUploadStatus({ type: 'info', message: 'Uploading...' });

      // Upload the screenshot
      const result = await uploadScreenshot(blob, patientId);

      // Parse the result to get the URL
      let uploadedUrl = '';
      try {
        const resultObj = JSON.parse(result);

        // Check for different response formats
        if (resultObj.data && resultObj.data.length > 0 && resultObj.data[0].Location) {
          // Format from the current API response
          uploadedUrl = resultObj.data[0].Location;
        } else if (resultObj.url) {
          uploadedUrl = resultObj.url;
        } else if (resultObj.imageUrl) {
          uploadedUrl = resultObj.imageUrl;
        } else if (resultObj.fileUrl) {
          uploadedUrl = resultObj.fileUrl;
        } else if (resultObj.data && resultObj.data.url) {
          uploadedUrl = resultObj.data.url;
        }
      } catch (e) {
        // If the result is not JSON, assume it's the URL directly
        uploadedUrl = result;
      }

      // Log the URL for debugging
      console.log("Image URL:", uploadedUrl);

      setUploadStatus({
        type: 'success',
        message: 'Screenshot uploaded successfully!',
        url: uploadedUrl
      });

      // Reset status after 5 seconds
      setTimeout(() => {
        setUploadStatus(null);
      }, 5000);
    } catch (error) {
      console.error('Screenshot error:', error);
      setUploadStatus({
        type: 'error',
        message: `Error: ${error.message || 'Failed to upload screenshot'}`
      });

      // Reset error status after 5 seconds
      setTimeout(() => {
        setUploadStatus(null);
      }, 5000);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="screenshot-container">
      <button
        className={`screenshot-button ${isUploading ? 'uploading' : ''}`}
        onClick={handleScreenshot}
        disabled={isUploading || !renderer}
      >
        {isUploading ? (
          <span className="loading-spinner"></span>
        ) : (
          <>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M23 19C23 19.5304 22.7893 20.0391 22.4142 20.4142C22.0391 20.7893 21.5304 21 21 21H3C2.46957 21 1.96086 20.7893 1.58579 20.4142C1.21071 20.0391 1 19.5304 1 19V8C1 7.46957 1.21071 6.96086 1.58579 6.58579C1.96086 6.21071 2.46957 6 3 6H7L9 3H15L17 6H21C21.5304 6 22.0391 6.21071 22.4142 6.58579C22.7893 6.96086 23 7.46957 23 8V19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M12 17C14.2091 17 16 15.2091 16 13C16 10.7909 14.2091 9 12 9C9.79086 9 8 10.7909 8 13C8 15.2091 9.79086 17 12 17Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Screenshot
          </>
        )}
      </button>

      {uploadStatus && (
        <div className={`upload-status ${uploadStatus.type}`}>
          <p>{uploadStatus.message}</p>
          {uploadStatus.url && (
            <a
              href={uploadStatus.url}
              target="_blank"
              rel="noopener noreferrer"
              className="view-link"
            >
              View Image
            </a>
          )}
        </div>
      )}
    </div>
  );
};

export default ScreenshotButton;
