import { Vector3 } from "three";
import React, { useRef, useEffect, useState } from "react";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { useFrame } from "@react-three/fiber";
import * as THREE from "three";
import { createBlackMaterial, createTransparentMaterial, createRoughWhiteMaterial, createGoldMaterial, createCompositeMaterial, createMetalMaterial, createMaterial1, createMaterial2, createMaterial3, createMaterial4, createMaterial5, createMaterial6 } from "../constants/materials";
import { teethNumbersWithNoTreatment } from "../constants/dictionaries";

export const createDefaultMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffffff,
    metalness: 0.3,
    roughness: 0.2,
    transparent: false,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    depthWrite: true,
  });

export function ChartingTeeth({ patientTeeth, setPatientTeeth, pointersRef, currentView, teethView, teethRef, selectedTooth, setSelectedTooth, bridgeStart, setBridgeStart, eraserToolActive, selectedTreatment, chartingThirdRow, missingToothActive, setMissingToothActive, resetTooth, setResetTooth, keyDown, chartingMDChildPointersRef }) {
  const internalTeethRef = useRef(new Map());
  const loaderRef = useRef(new GLTFLoader());
  
  useEffect(() => {
    if (teethRef) {
      teethRef.current = internalTeethRef.current;
    }
  }, [teethRef]);

  const loadToothModel = async (number, treatment, pointer, isDefaultTransparent = false) => {
    let treatmentMaterial;
    let treatmentName = treatment.name;
    if (treatment.name.includes("Veneers")){
        treatmentMaterial = treatment.name.split("_")[1];
        treatmentName = "Veneers"
    }
    if (treatment.name.includes("CrownForModedTooth")) {
        treatmentMaterial = treatment.name.split("_")[1];
        treatmentName = "CrownForModedTooth";
    }

    let prefixPath ="";
    if (teethView === 'front' && (treatmentName.includes("BoneGraftingBlock") || treatmentName.includes("MarylandBridge_C") || treatmentName.includes("MarylandWing_C"))) {
        prefixPath = "FV";
    } else if (teethView === 'top' && treatmentName.includes("Extraction")) {
        prefixPath = "TV";
    }

    const modelPath = treatmentName === "Default" ?
    `./TreatmentsV4/${treatmentName}/${number}.glb`
    : `./TreatmentsV4/${treatmentName}/${number}_${prefixPath + treatmentName}.glb`

    try {
      const gltf = await new Promise((resolve, reject) => {
        loaderRef.current.load(modelPath, resolve, undefined, reject);
      });

      if (gltf.scene.children.length === 0) {
        console.error(`No children in model for tooth ${number}`);
        return;
      }

      let child = gltf.scene.children[0];
      child.position.set(0, 0, 0);
      child.rotation.set(0, 0, 0);
      // Set a unique name for the tooth mesh
      child.name = `tooth_${number}_${treatmentName}`;

      if (child.isMesh) {
        const originalMaterial = child.material.clone();
        originalMaterial.side = THREE.FrontSide;
        originalMaterial.depthWrite = true;
        let childMaterial = originalMaterial;
        if (treatmentName === "Default" && isDefaultTransparent){
            childMaterial = createTransparentMaterial();
        }
        if (treatmentName === "ClosedGap") {
            childMaterial = createBlackMaterial();
        }
        if (treatmentName === "PinRetention" || treatmentName === "RootCanalTreatment" ) {
            childMaterial = createTransparentMaterial();
        }
        if (treatmentName === "RetainedRoot") {
            childMaterial = createRoughWhiteMaterial();
        }
        if (treatmentName === "Veneers"){
            if (treatmentMaterial === "Emax"){
                childMaterial = createMaterial1();
            } else if (treatmentMaterial === "Empress"){
                childMaterial = createMaterial2();
            } else if (treatmentMaterial === "Composite"){
                childMaterial = createCompositeMaterial();
            } else if (treatmentMaterial === "Gold"){
                childMaterial = createGoldMaterial();
            } else if (treatmentMaterial === "Porcelain"){
                childMaterial = createMaterial3();
            } else if (treatmentMaterial === "Silver"){
                childMaterial = createMetalMaterial();
            } else if (treatmentMaterial === "Temporary"){
                childMaterial = createMaterial4();
            } else if (treatmentMaterial === "Zirconia"){
                childMaterial = createMaterial5();
            } else {
                childMaterial = createMaterial6();
            }
        }
        if (treatmentName === "CrownForModedTooth") {
            if (treatmentMaterial === "Gold") {
                childMaterial = createGoldMaterial();
            } else if (treatmentMaterial === "Metal") {
                childMaterial = createMetalMaterial();
            }
        }

        child.material = childMaterial;
        child.userData = {
          number: number,
          type: "tooth",
          isInteractive: true,
          originalMaterial: childMaterial
        };
        child.material.side= THREE.DoubleSide

      }

      if (number >= 9 && number <= 24 && !treatmentName.includes("BoneGraftingBlock")) {
        const originalScale = new Vector3();
        child.getWorldScale(originalScale);
        child.scale.set(1, 1, 1);
        child.updateMatrixWorld(true);
        child.scale.set(
        -Math.abs(originalScale.x),
        -Math.abs(originalScale.y),
        -Math.abs(originalScale.z)
        );
      }

      pointer.add(child);

      internalTeethRef.current.set(parseInt(number, 10), child);
    } catch (error) {
      console.error(`Error loading tooth ${number}:`, error);
    }
  };

  useEffect(() => {
    if (pointersRef?.current) {
         // Clear any existing children from pointers
        pointersRef.current.forEach(pointer => {
            while (pointer.children.length > 0) {
            const child = pointer.children[0];
            pointer.remove(child);
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
                if (Array.isArray(child.material)) {
                child.material.forEach(material => material.dispose());
                } else {
                child.material.dispose();
                }
            }
            }
        });
        if (teethView === 'top' || teethView === 'front' || (teethView === 'back' && chartingThirdRow)) {
            // Load new teeth
            Object.entries(patientTeeth).forEach(([number, toothData]) => {
                if (number !== 'patientType' && number !== 'patientId'){
                    const pointer = pointersRef.current.get(parseInt(number, 10));
                    if (pointer && !toothData.marked_as_missing){
                        if (toothData.treatments.length === 0) {
                            loadToothModel(number, {name: "Default"}, pointer);
                        } else {
                            toothData.treatments.forEach(treatment => {
                                if (!teethNumbersWithNoTreatment[treatment.name] || !teethNumbersWithNoTreatment[treatment.name].includes(number)){
                                    loadToothModel(number, treatment, pointer);
                                    if (treatment.need_default_tooth) {
                                        let isDefaultTransparent = false;
                                        if (treatment.name === "Post&Core") {
                                            isDefaultTransparent = true;
                                        }
                                        loadToothModel(number, {name: "Default"}, pointer, isDefaultTransparent);
                                    }
                                }
                            });
                        }
                    }
                }
            });

            // Apply opacity levels to treatments
            setTimeout(() => {
                Object.entries(patientTeeth).forEach(([number, toothData]) => {
                    if (number !== 'patientType' && number !== 'patientId'){
                        applyMaterialOpacityLevels(toothData.treatments, number);
                    }
                });
            }, 500);
        }
    }
  }, [chartingThirdRow]);

  const handleMissingTooth = (toothNumber) => {
    
    // Remove from both front and top view
    pointersRef.current.forEach((pointer, key) => {
        if (key === toothNumber) {
            // Iterate in reverse to avoid skipping elements
            for (let i = pointer.children.length - 1; i >= 0; i--) {
                const child = pointer.children[i];
                if (child.name.startsWith(`tooth_${toothNumber}_`)) {
                    pointer.remove(child);

                    // Dispose of geometry and materials
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => material.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                }
            }
        }
    });

    // remove mixed dentition if it exists
    if (patientTeeth[toothNumber]?.mixed_dentition) {
        const pointer = chartingMDChildPointersRef.current.get(parseInt(toothNumber, 10));
        for (let i = pointer.children.length - 1; i >= 0; i--) {
            const child = pointer.children[i];
            if (child.name === `${toothNumber}C`) {
                pointer.remove(child);

                // Dispose of geometry and materials
                if (child.geometry) child.geometry.dispose();
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(material => material.dispose());
                    } else {
                        child.material.dispose();
                    }
                }
            }
        }
    }

    // Update state to mark the tooth as missing and remove all treatments
    setPatientTeeth(prevTeeth => {
        const newTeeth = { ...prevTeeth };
        newTeeth[toothNumber].marked_as_missing = true;
        newTeeth[toothNumber].mixed_dentition = false;
        return newTeeth;
    });
  }

  const handleResetTooth = (toothNumber) => {

    pointersRef.current.forEach((pointer, key) => {
        if (key === toothNumber) {
            // Iterate in reverse to avoid skipping elements
            for (let i = pointer.children.length - 1; i >= 0; i--) {
                const child = pointer.children[i];
                if (child.name.startsWith(`tooth_${toothNumber}_`)) {
                    pointer.remove(child);

                    // Dispose of geometry and materials
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => material.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                }
            }
        }
    });

    const pointer = pointersRef.current.get(parseInt(toothNumber, 10));
    loadToothModel(toothNumber, {name: "Default"}, pointer);
  }

  const handleEraserTool = () => {
    if (!eraserToolActive || !selectedTooth) return; // Ensure eraser tool is active

    const toothNumber = selectedTooth;

    // Remove treatment from both front and top views
    // Get the last treatment name
    const lastTreatment = patientTeeth[toothNumber].treatments.at(-1)?.name;
    pointersRef.current.forEach((pointer, key) => {
        if (key === toothNumber) {
            // Collect children to remove
            const childrenToRemove = pointer.children.filter(child => 
                child.name === `tooth_${toothNumber}_${lastTreatment}`
            );

            // Remove them safely
            childrenToRemove.forEach(child => {
                pointer.remove(child);
                
                // Dispose geometry and materials properly
                if (child.geometry) child.geometry.dispose();
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(material => material.dispose());
                    } else {
                        child.material.dispose();
                    }
                }
            });
        }
    });

    // Load default if no treatments left - this is checked if the treatments list has 1 last treatment remaining
    if (patientTeeth[toothNumber]?.treatments.length === 1) {
        const pointer = pointersRef.current.get(parseInt(toothNumber, 10));
        loadToothModel(toothNumber, {name: "Default"}, pointer);
    }

    // apply opacity levels to the remaining treatments
    const remainingTreatments = patientTeeth[toothNumber]?.treatments.slice(0, -1);
    if (remainingTreatments) {
        setTimeout(() => {
            applyMaterialOpacityLevels(remainingTreatments, selectedTooth);
        }, 10);
    }

    // Update state to remove the last treatment
    if (teethView === 'top') {
        setPatientTeeth(prevTeeth => {
            const newTeeth = { ...prevTeeth };
    
            if (newTeeth[toothNumber]?.treatments.length > 0) {
    
                // Remove last treatment from state
                newTeeth[toothNumber].treatments = newTeeth[toothNumber].treatments.slice(0, -1);
            }
            return newTeeth;
        });
    }
        
};

const handleSelectedTreatment = () => {
    if (!selectedTreatment || !selectedTooth) return;

    if (keyDown === 'r' && !bridgeStart && selectedTreatment.bridge_treatment) {
        setBridgeStart(selectedTooth);
        return;
    }

    if (patientTeeth[selectedTooth]?.treatments.some(treatment => treatment.name === selectedTreatment.name)) return;

    const toothNumber = selectedTooth;
    const pointer = pointersRef.current.get(parseInt(toothNumber, 10));

    if (keyDown === 'r' && bridgeStart && selectedTreatment.bridge_treatment) {
        for (let i = Number(bridgeStart); i <= Number(toothNumber); i++) {
            const tooth  = i.toString();
            const pointer = pointersRef.current.get(parseInt(tooth, 10));

            // Remove default teeth before adding treatment if no treatments belong to teeth
            if (patientTeeth[tooth]?.treatments.length === 0) {
                pointer.children.forEach(child => {
                    if (child.name.startsWith(`tooth_${tooth}_`)) {
                        pointer.remove(child);
                        if (child.geometry) child.geometry.dispose();
                        if (child.material) {
                            if (Array.isArray(child.material)) {
                                child.material.forEach(material => material.dispose());
                            } else {
                                child.material.dispose();
                            }
                        }
                    }
                });
            }
            
            // Load the new treatment model
            loadToothModel(tooth, selectedTreatment, pointer);

            // Apply opacity levels to treatments
            const UpdatedListOfTreatments = patientTeeth[tooth]?.treatments.concat(selectedTreatment);
            if (UpdatedListOfTreatments) {
                setTimeout(() => {
                    applyMaterialOpacityLevels(UpdatedListOfTreatments, tooth);
                }, 10);
            }

            // Update state to add the new treatment
            if (teethView === 'top') {
                setPatientTeeth(prevTeeth => {
                    const newTeeth = { ...prevTeeth };
                    newTeeth[tooth].treatments.push(selectedTreatment);
                    return newTeeth;
                });
            }
        }
        setBridgeStart(null);
    } else {
        // Remove default teeth before adding treatment if no treatments belong to teeth
        if (patientTeeth[toothNumber]?.treatments.length === 0) {
            pointer.children.forEach(child => {
                if (child.name.startsWith(`tooth_${toothNumber}_`)) {
                    pointer.remove(child);
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => material.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                }
            });
        }
        
        // Load the new treatment model
        loadToothModel(toothNumber, selectedTreatment, pointer);

        // Apply opacity levels to treatments
        const UpdatedListOfTreatments = patientTeeth[toothNumber]?.treatments.concat(selectedTreatment);
        if (UpdatedListOfTreatments) {
            setTimeout(() => {
                applyMaterialOpacityLevels(UpdatedListOfTreatments, toothNumber);
            }, 10);
        }

        // Update state to add the new treatment
        if (teethView === 'top') {
            setPatientTeeth(prevTeeth => {
                const newTeeth = { ...prevTeeth };
                newTeeth[toothNumber].treatments.push(selectedTreatment);
                return newTeeth;
            });
        }
    }
}

const applyMaterialOpacityLevels = (treatments, tooth) => {
    if (!tooth || treatments.length === 0) return;
    
    const toothNumber = tooth;
    const totalTreatments = treatments.length;

    // Define opacity range
    const minOpacity = 0.5;
    const maxOpacity = 1;

    // go over treatments available on selected tooth and set different opacity levels for each
    const opacityStep = totalTreatments > 1 ? (maxOpacity - minOpacity) / (totalTreatments - 1) : 0;

    treatments.forEach((treatment, index) => {
        const treatmentOpacity = maxOpacity - index * opacityStep;
        pointersRef.current.forEach((pointer) => {
            pointer.children.forEach(child => {
                if (child.name === `tooth_${toothNumber}_${treatment.name}`) {
                    if (child.material && child.material.opacity === 1) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => {
                                material.transparent = true;
                                material.opacity = treatmentOpacity;
                            });
                        } else {
                            child.material.transparent = true;
                            child.material.opacity = treatmentOpacity;
                        }
                    }
                }
            });
        });
    });
}

    // Attach event listeners to each tooth mesh
    useEffect(() => {
        if (selectedTooth) {
            if (teethView === 'top' || teethView === 'front' || (teethView === 'back' && chartingThirdRow)) {
                if (eraserToolActive) {
                    handleEraserTool();
                } else if (selectedTreatment) {
                    handleSelectedTreatment();
                }

                // Prevent double execution by delaying the reset
                setTimeout(() => {
                    setSelectedTooth(null);
                }, 0);
            }
        }
    }, [selectedTooth]);

    useEffect(() => {
      if (missingToothActive) {
        handleMissingTooth(missingToothActive);
        setMissingToothActive(null);
      }
    }, [missingToothActive])

    useEffect(() => {
        if (resetTooth) {
            if (teethView === 'top' || teethView === 'front' || (teethView === 'back' && chartingThirdRow)) {
                handleResetTooth(resetTooth);
                setResetTooth(null);
            }
        }
      }, [resetTooth])

  return null;
}

ChartingTeeth.displayName = 'ChartingTeeth';
export default ChartingTeeth;