import { Vector3 } from "three";
import React, { useRef, useEffect, useState } from "react";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { useFrame } from "@react-three/fiber";
import * as THREE from "three";

export const createDefaultMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffffff,
    metalness: 0.3,
    roughness: 0.2,
    transparent: false,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    depthWrite: true,
  });

export function ChartingTeeth({ patientTeeth, setPatientTeeth, pointersRef, currentView, teethView, teethRef, selectedTooth, setSelectedTooth, bridgeStart, setBridgeStart, eraserToolActive, selectedTreatment, chartingThirdRow, missingToothActive, setMissingToothActive, resetTooth, setResetTooth, keyDown, chartingMDChildPointersRef }) {
  const internalTeethRef = useRef(new Map());
  const loaderRef = useRef(new GLTFLoader());
  
  useEffect(() => {
    if (teethRef) {
      teethRef.current = internalTeethRef.current;
    }
  }, [teethRef]);

  const loadToothModel = async (number, treatment, pointer) => {
    const modelPath = treatment.name === "Default" ?
    `https://upod.s3.eu-central-1.amazonaws.com/treatmentsV3/${treatment.name}/${number}.glb`
    : `https://upod.s3.eu-central-1.amazonaws.com/treatmentsV3/${treatment.name}/${number}_${treatment.name}.glb`

    try {
      const gltf = await new Promise((resolve, reject) => {
        loaderRef.current.load(modelPath, resolve, undefined, reject);
      });

      if (gltf.scene.children.length === 0) {
        console.error(`No children in model for tooth ${number}`);
        return;
      }

      const child = gltf.scene.children[0];
      child.position.set(0, 0, 0);
      child.rotation.set(0, 0, 0);
      // Set a unique name for the tooth mesh
      child.name = `tooth_${number}_${treatment.name}`;

      if (child.isMesh) {
        const originalMaterial = child.material.clone();
        originalMaterial.side = THREE.FrontSide;
        originalMaterial.depthWrite = true;
        child.material = originalMaterial;
        child.userData = {
          number: number,
          type: "tooth",
          isInteractive: true,
          originalMaterial: originalMaterial
        };
        child.material.side= THREE.DoubleSide

      }

      if (number >= 9 && number <= 24) {
        const originalScale = new Vector3();
        child.getWorldScale(originalScale);
        child.scale.set(1, 1, 1);
        child.updateMatrixWorld(true);
        child.scale.set(
        -Math.abs(originalScale.x),
        -Math.abs(originalScale.y),
        -Math.abs(originalScale.z)
        );
      }

      pointer.add(child);

      internalTeethRef.current.set(parseInt(number, 10), child);
    } catch (error) {
      console.error(`Error loading tooth ${number}:`, error);
    }
  };

  useEffect(() => {
    if (pointersRef?.current) {
         // Clear any existing children from pointers
        pointersRef.current.forEach(pointer => {
            while (pointer.children.length > 0) {
            const child = pointer.children[0];
            pointer.remove(child);
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
                if (Array.isArray(child.material)) {
                child.material.forEach(material => material.dispose());
                } else {
                child.material.dispose();
                }
            }
            }
        });
        if (teethView === 'top' || teethView === 'front' || (teethView === 'back' && chartingThirdRow)) {
            // Load new teeth
            Object.entries(patientTeeth).forEach(([number, toothData]) => {
            const pointer = pointersRef.current.get(parseInt(number, 10));
            if (pointer && !toothData.marked_as_missing){
                if (toothData.treatments.length === 0) {
                    loadToothModel(number, {name: "Default"}, pointer);
                } else {
                    toothData.treatments.forEach(treatment => {
                        loadToothModel(number, treatment, pointer);
                    });
                }
            } 
            });

            // Apply opacity levels to treatments
            setTimeout(() => {
                Object.entries(patientTeeth).forEach(([number, toothData]) => {
                    applyMaterialOpacityLevels(toothData.treatments, number);
                });
            }, 500);
        }
    }
  }, [chartingThirdRow]);

  const handleMissingTooth = (toothNumber) => {
    
    // Remove from both front and top view
    pointersRef.current.forEach((pointer, key) => {
        if (key === toothNumber) {
            // Iterate in reverse to avoid skipping elements
            for (let i = pointer.children.length - 1; i >= 0; i--) {
                const child = pointer.children[i];
                if (child.name.startsWith(`tooth_${toothNumber}_`)) {
                    pointer.remove(child);

                    // Dispose of geometry and materials
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => material.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                }
            }
        }
    });

    // remove mixed dentition if it exists
    if (patientTeeth[toothNumber]?.mixed_dentition) {
        const pointer = chartingMDChildPointersRef.current.get(parseInt(toothNumber, 10));
        for (let i = pointer.children.length - 1; i >= 0; i--) {
            const child = pointer.children[i];
            if (child.name === `${toothNumber}C`) {
                pointer.remove(child);

                // Dispose of geometry and materials
                if (child.geometry) child.geometry.dispose();
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(material => material.dispose());
                    } else {
                        child.material.dispose();
                    }
                }
            }
        }
    }

    // Update state to mark the tooth as missing and remove all treatments
    setPatientTeeth(prevTeeth => {
        const newTeeth = { ...prevTeeth };
        newTeeth[toothNumber].marked_as_missing = true;
        newTeeth[toothNumber].mixed_dentition = false;
        return newTeeth;
    });
  }

  const handleResetTooth = (toothNumber) => {

    pointersRef.current.forEach((pointer, key) => {
        if (key === toothNumber) {
            // Iterate in reverse to avoid skipping elements
            for (let i = pointer.children.length - 1; i >= 0; i--) {
                const child = pointer.children[i];
                if (child.name.startsWith(`tooth_${toothNumber}_`)) {
                    pointer.remove(child);

                    // Dispose of geometry and materials
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => material.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                }
            }
        }
    });

    const pointer = pointersRef.current.get(parseInt(toothNumber, 10));
    loadToothModel(toothNumber, {name: "Default"}, pointer);
  }

  const handleEraserTool = () => {
    if (!eraserToolActive || !selectedTooth) return; // Ensure eraser tool is active

    const toothNumber = selectedTooth;

    // Remove treatment from both front and top views
    // Get the last treatment name
    const lastTreatment = patientTeeth[toothNumber].treatments.at(-1)?.name;
    pointersRef.current.forEach((pointer, key) => {
        if (key === toothNumber) {
            // Collect children to remove
            const childrenToRemove = pointer.children.filter(child => 
                child.name === `tooth_${toothNumber}_${lastTreatment}`
            );

            // Remove them safely
            childrenToRemove.forEach(child => {
                pointer.remove(child);
                
                // Dispose geometry and materials properly
                if (child.geometry) child.geometry.dispose();
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(material => material.dispose());
                    } else {
                        child.material.dispose();
                    }
                }
            });
        }
    });

    // Load default if no treatments left - this is checked if the treatments list has 1 last treatment remaining
    if (patientTeeth[toothNumber]?.treatments.length === 1) {
        const pointer = pointersRef.current.get(parseInt(toothNumber, 10));
        loadToothModel(toothNumber, {name: "Default"}, pointer);
    }

    // apply opacity levels to the remaining treatments
    const remainingTreatments = patientTeeth[toothNumber]?.treatments.slice(0, -1);
    if (remainingTreatments) {
        setTimeout(() => {
            applyMaterialOpacityLevels(remainingTreatments, selectedTooth);
        }, 10);
    }

    // Update state to remove the last treatment
    if (teethView === 'top') {
        setPatientTeeth(prevTeeth => {
            const newTeeth = { ...prevTeeth };
    
            if (newTeeth[toothNumber]?.treatments.length > 0) {
    
                // Remove last treatment from state
                newTeeth[toothNumber].treatments = newTeeth[toothNumber].treatments.slice(0, -1);
            }
            return newTeeth;
        });
    }
        
};

const handleSelectedTreatment = () => {
    if (!selectedTreatment || !selectedTooth) return;

    if (keyDown === 'r' && !bridgeStart && selectedTreatment.bridge_treatment) {
        setBridgeStart(selectedTooth);
        return;
    }

    if (patientTeeth[selectedTooth]?.treatments.some(treatment => treatment.name === selectedTreatment.name)) return;

    const toothNumber = selectedTooth;
    const pointer = pointersRef.current.get(parseInt(toothNumber, 10));

    if (keyDown === 'r' && bridgeStart && selectedTreatment.bridge_treatment) {
        for (let i = Number(bridgeStart); i <= Number(toothNumber); i++) {
            const tooth  = i.toString();
            const pointer = pointersRef.current.get(parseInt(tooth, 10));

            // Remove default teeth before adding treatment if no treatments belong to teeth
            if (patientTeeth[tooth]?.treatments.length === 0) {
                pointer.children.forEach(child => {
                    if (child.name.startsWith(`tooth_${tooth}_`)) {
                        pointer.remove(child);
                        if (child.geometry) child.geometry.dispose();
                        if (child.material) {
                            if (Array.isArray(child.material)) {
                                child.material.forEach(material => material.dispose());
                            } else {
                                child.material.dispose();
                            }
                        }
                    }
                });
            }
            
            // Load the new treatment model
            loadToothModel(tooth, selectedTreatment, pointer);

            // Apply opacity levels to treatments
            const UpdatedListOfTreatments = patientTeeth[tooth]?.treatments.concat(selectedTreatment);
            if (UpdatedListOfTreatments) {
                setTimeout(() => {
                    applyMaterialOpacityLevels(UpdatedListOfTreatments, tooth);
                }, 10);
            }

            // Update state to add the new treatment
            if (teethView === 'top') {
                setPatientTeeth(prevTeeth => {
                    const newTeeth = { ...prevTeeth };
                    newTeeth[tooth].treatments.push(selectedTreatment);
                    return newTeeth;
                });
            }
        }
        setBridgeStart(null);
    } else {
        // Remove default teeth before adding treatment if no treatments belong to teeth
        if (patientTeeth[toothNumber]?.treatments.length === 0) {
            pointer.children.forEach(child => {
                if (child.name.startsWith(`tooth_${toothNumber}_`)) {
                    pointer.remove(child);
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => material.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                }
            });
        }
        
        // Load the new treatment model
        loadToothModel(toothNumber, selectedTreatment, pointer);

        // Apply opacity levels to treatments
        const UpdatedListOfTreatments = patientTeeth[toothNumber]?.treatments.concat(selectedTreatment);
        if (UpdatedListOfTreatments) {
            setTimeout(() => {
                applyMaterialOpacityLevels(UpdatedListOfTreatments, toothNumber);
            }, 10);
        }

        // Update state to add the new treatment
        if (teethView === 'top') {
            setPatientTeeth(prevTeeth => {
                const newTeeth = { ...prevTeeth };
                newTeeth[toothNumber].treatments.push(selectedTreatment);
                return newTeeth;
            });
        }
    }
}

const applyMaterialOpacityLevels = (treatments, tooth) => {
    if (!tooth || treatments.length === 0) return;
    
    const toothNumber = tooth;
    const totalTreatments = treatments.length;

    // Define opacity range
    const minOpacity = 0.5;
    const maxOpacity = 1;

    // go over treatments available on selected tooth and set different opacity levels for each
    const opacityStep = totalTreatments > 1 ? (maxOpacity - minOpacity) / (totalTreatments - 1) : 0;

    treatments.forEach((treatment, index) => {
        const treatmentOpacity = maxOpacity - index * opacityStep;
        pointersRef.current.forEach((pointer) => {
            pointer.children.forEach(child => {
                if (child.name === `tooth_${toothNumber}_${treatment.name}`) {
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => {
                                material.transparent = true;
                                material.opacity = treatmentOpacity;
                            });
                        } else {
                            child.material.transparent = true;
                            child.material.opacity = treatmentOpacity;
                        }
                    }
                }
            });
        });
    });
}

    // Attach event listeners to each tooth mesh
    useEffect(() => {
        if (selectedTooth) {
            if (teethView === 'top' || teethView === 'front' || (teethView === 'back' && chartingThirdRow)) {
                if (eraserToolActive) {
                    handleEraserTool();
                } else if (selectedTreatment) {
                    handleSelectedTreatment();
                }

                // Prevent double execution by delaying the reset
                setTimeout(() => {
                    setSelectedTooth(null);
                }, 0);
            }
        }
    }, [selectedTooth]);

    useEffect(() => {
      if (missingToothActive) {
        handleMissingTooth(missingToothActive);
        setMissingToothActive(null);
      }
    }, [missingToothActive])

    useEffect(() => {
        if (resetTooth) {
            if (teethView === 'top' || teethView === 'front' || (teethView === 'back' && chartingThirdRow)) {
                handleResetTooth(resetTooth);
                setResetTooth(null);
            }
        }
      }, [resetTooth])

  return null;
}

ChartingTeeth.displayName = 'ChartingTeeth';
export default ChartingTeeth;