import { Vector3 } from "three";
import React, {
  useRef,
  useEffect,
  useState,
  forwardRef,
  useCallback,
} from "react";
import { useTeeth } from "../context/TeethContext";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { useFrame, useThree } from "@react-three/fiber";
import { BASE_URL, TREATMENTS_VERSION } from "../config/api";
import * as THREE from "three";

// Import utility functions from modelUtils.js
import {
  modelCache,
  animationCache,
  createYPositionOnlyClip,
  getModelParts,
  cloneModel,
  findExactSurfaceMorphTarget,
  forceSceneUpdate,
  clearAllTeethFromScene,
  checkForStateSpecificModel,
} from "../utils/modelUtils";
import { determineHealthyPartVisibility } from "../utils/treatmentUtils";

// Import message utility functions
import { addTeethClearedListener } from "../utils/messageUtils";

// Import material utilities
import { applyAntiZFightingProperties } from "../utils/materialUtils";

const SkullJawTeeth = forwardRef(
  (
    { patientTeeth, pointersRef, currentView, teethRefStateA: teethRef },
    ref,
  ) => {
    // Get teeth data from context
    const { getTreatmentVisibility } = useTeeth();

    // Get the patient type from the teeth data
    const patientType = patientTeeth.patientType || "ADULT";
    const internalTeethRef = useRef(new Map());
    const loaderRef = useRef(new GLTFLoader());
    const mountedRef = useRef(true);
    const mixersRef = useRef(new Map());
    const actionsRef = useRef(new Map());
    const frameCountRef = useRef(0);
    const [isPlaying, setIsPlaying] = useState(false);
    const [isPaused, setIsPaused] = useState(false);
    const [loadingTeeth, setLoadingTeeth] = useState(new Map());
    const [isLoading, setIsLoading] = useState(false);
    const totalTeethToLoadRef = useRef(0);
    const isInitializedRef = useRef(false);

    // We're using the imported findExactSurfaceMorphTarget function from modelUtils.js

    // Define cleanupTeeth function first
    // Get access to the scene and renderer from React Three Fiber
    const { scene, gl: renderer, camera } = useThree();

    const cleanupTeeth = useCallback(() => {
      // Cleanup teeth

      // Always clean up mixers and actions
      mixersRef.current.forEach((mixer) => mixer.stopAllAction());
      mixersRef.current.clear();
      actionsRef.current.clear();

      // Always clear the refs
      internalTeethRef.current.clear();

      if (teethRef && teethRef.current) {
        teethRef.current.clear();
      }

      // Use the utility function to clear all teeth from the scene
      if (scene) {
        clearAllTeethFromScene(scene);
      }

      // Force a scene update using the utility function
      if (renderer && scene && camera) {
        forceSceneUpdate(renderer, scene, camera);
      }

      // Cleanup complete
    }, [
      internalTeethRef,
      teethRef,
      mixersRef,
      actionsRef,
      scene,
      renderer,
      camera,
    ]);

    // Setup resource path and event listeners
    useEffect(() => {
      loaderRef.current.setResourcePath(`${BASE_URL}/${TREATMENTS_VERSION}/`);
      loaderRef.current.setCrossOrigin("anonymous");

      // Add event listener for teeth-cleared event using the utility function
      const removeTeethClearedListener = addTeethClearedListener(() => {
        cleanupTeeth();
      });

      return () => {
        // Clean up the event listener
        removeTeethClearedListener();
      };
    }, [cleanupTeeth]);

    // Update teethRef when internalTeethRef changes
    useEffect(() => {
      if (teethRef && teethRef.current && internalTeethRef.current.size > 0) {
        internalTeethRef.current.forEach((tooth, number) => {
          teethRef.current.set(number, tooth);
        });
      }
    }, [internalTeethRef.current.size, currentView, teethRef]);

    const playAnimations = useCallback(() => {
      if (!isPaused) {
        frameCountRef.current = 0;
      }

      actionsRef.current.forEach((action) => {
        action.stop();
        action.reset();
      });

      actionsRef.current.forEach((action) => {
        action.play();
        action.setEffectiveWeight(1);
        action.paused = false;
      });

      setIsPlaying(true);
      setIsPaused(false);
    }, [isPaused, actionsRef, setIsPlaying, setIsPaused]);

    const pauseAnimations = () => {
      actionsRef.current.forEach((action) => {
        action.paused = true;
      });
      setIsPaused(true);
    };

    const resetAnimations = () => {
      frameCountRef.current = 0;

      actionsRef.current.forEach((action) => {
        action.stop();
        action.reset();
      });

      setIsPlaying(false);
      setIsPaused(false);
    };

    const processAnimations = useCallback(
      (toothNumber, animations, scene, treatmentName) => {
        if (!animations || animations.length === 0) return;

        // Check if a mixer already exists for this tooth
        let mixer = mixersRef.current.get(toothNumber);
        if (!mixer) {
          // Create a new mixer if one doesn't exist
          mixer = new THREE.AnimationMixer(scene);
          mixersRef.current.set(toothNumber, mixer);
        }

        animations.forEach((clip) => {
          const validTracks = clip.tracks.filter((track) => {
            const nodeName = track.name.split(".")[0];
            return scene.getObjectByName(nodeName);
          });

          if (validTracks.length === 0) return;

          const validClip = new THREE.AnimationClip(
            clip.name,
            clip.duration,
            validTracks,
          );

          // Create a unique key for this action that includes the treatment name
          const actionKey = `${toothNumber}_${treatmentName}_${validClip.name}`;

          // Check if this action already exists
          if (!actionsRef.current.has(actionKey)) {
            const action = mixer.clipAction(validClip);
            action.setLoop(THREE.LoopOnce);
            action.clampWhenFinished = true;

            actionsRef.current.set(actionKey, action);
          }
        });
      },
      [mixersRef, actionsRef],
    );

    useFrame((_, delta) => {
      if (isPlaying && !isPaused) {
        mixersRef.current.forEach((mixer) => mixer.update(delta));

        const fps = 30;
        frameCountRef.current += delta * fps;

        if (frameCountRef.current >= 30) {
          pauseAnimations();
        }
      }
    });

    useEffect(() => {
      const setupAnimationControls = () => {
        const teethControls = {
          play: playAnimations,
          pause: pauseAnimations,
          reset: resetAnimations,
          isPlaying: () => isPlaying,
          isPaused: () => isPaused,
          currentFrame: () => frameCountRef.current,
        };

        window.teethAnimationControls = teethControls;

        if (window.jawAnimationControls) {
          const originalJawPlay = window.jawAnimationControls.play;
          window.jawAnimationControls.play = () => {
            originalJawPlay();
            playAnimations();
          };

          window.jawAnimationControls._originalPlay = originalJawPlay;

          const originalJawReset = window.jawAnimationControls.reset;
          window.jawAnimationControls.reset = () => {
            originalJawReset();
            resetAnimations();
          };
          window.jawAnimationControls._originalReset = originalJawReset;
        }
      };

      setupAnimationControls();

      return () => {
        if (window.jawAnimationControls) {
          if (window.jawAnimationControls._originalPlay) {
            window.jawAnimationControls.play =
              window.jawAnimationControls._originalPlay;
          }
          if (window.jawAnimationControls._originalReset) {
            window.jawAnimationControls.reset =
              window.jawAnimationControls._originalReset;
          }
        }
      };
    }, [isPlaying, isPaused, playAnimations]);

    // Using the imported cloneModel function from modelUtils.js

    const markToothLoaded = useCallback(
      (number) => {
        if (!mountedRef.current) return;

        setLoadingTeeth((prev) => {
          const newMap = new Map(prev);
          newMap.set(number, true);
          return newMap;
        });
      },
      [mountedRef, setLoadingTeeth],
    );

    // Updated loadToothModel function with precise morph target handling and better error handling
    const loadToothModel = useCallback(
      async (number, toothData, pointer) => {
        // Skip teeth that are marked as missing or have a treatment with missing_tooth_indicator
        const hasMissingToothTreatment =
          toothData.treatments &&
          toothData.treatments.some(
            (treatment) => treatment.missing_tooth_indicator,
          );

        if (toothData.marked_as_missing || hasMissingToothTreatment) {
          // Mark the tooth as loaded even though we're not actually loading it
          markToothLoaded(number);
          return;
        }

        if (!mountedRef.current) return;

        const toothNumber = parseInt(number, 10);
        const existingTooth = internalTeethRef.current.get(toothNumber);
        if (existingTooth && existingTooth.parent === pointer) {
          markToothLoaded(number);
          return;
        }

        const existingParts = pointer.children.filter((c) =>
          c.name.startsWith(`tooth_${number}_`),
        );
        existingParts.forEach((part) => {
          pointer.remove(part);
          part.traverse((child) => {
            if (child.isMesh) {
              child.geometry?.dispose();
              if (Array.isArray(child.material)) {
                child.material.forEach((m) => m.dispose());
              } else {
                child.material?.dispose();
              }
            }
          });
        });

        // Sort treatments by creation date (newest first)
        const sortedTreatments = toothData.treatments
          ? [...toothData.treatments].sort((a, b) => {
              const dateA = new Date(a.created_at || 0);
              const dateB = new Date(b.created_at || 0);
              return dateB - dateA; // Newest first
            })
          : [];

        console.log(
          `[SkullJawTeeth] DEBUG: Tooth ${number} sortedTreatments (initial load):`,
          JSON.parse(
            JSON.stringify(
              sortedTreatments.map((t) => ({
                name: t.name,
                created_at: t.created_at,
                id: t.Id || t.id,
              })),
            ),
          ),
        );

        // Get model parts for all treatments
        const modelParts = getModelParts(toothData);
        let primaryModel = null;

        // Store decay surfaces for filling part (from the first/newest treatment)
        const decaySurfaces =
          sortedTreatments.length > 0 && sortedTreatments[0].surfaces
            ? Object.keys(sortedTreatments[0].surfaces)
            : [];

        for (const part of modelParts) {
          // Check if this treatment has state-specific models (A for skull, B for jaw)
          const hasStateSpecificModel = checkForStateSpecificModel(part);

          // Determine the correct model path based on the part, current view, and patient type
          // 'part' is the base treatment name (e.g., "Clasp", "Bridge", "Default")
          // 'number' is the tooth number (e.g., "9")
          // 'patientType' is "ADULT" or "CHILDREN"
          // 'currentView' is "skull" or "jaw"
          // 'hasStateSpecificModel' is a boolean derived from checkForStateSpecificModel(part)

          let relativeModelPath;
          const toothIdInFilename =
            patientType === "CHILDREN" ? `${number}C` : number;

          if (part === "Default") {
            const folder =
              patientType === "CHILDREN" ? "child/Default" : "Default";
            relativeModelPath = `${folder}/${toothIdInFilename}.glb`;
          } else if (hasStateSpecificModel) {
            // hasStateSpecificModel was already checked on line 346
            const stateSuffix = currentView === "skull" ? "_A" : "_B";
            // 'part' is already the base name, e.g., "Clasp"
            const treatmentFolderWithState = `${part}${stateSuffix}`; // e.g., Clasp_A
            const fileName = `${toothIdInFilename}_${part}${stateSuffix}.glb`; // e.g., 9C_Clasp_A.glb or 9_Clasp_A.glb

            const basePathPrefix =
              patientType === "CHILDREN"
                ? `child/${treatmentFolderWithState}`
                : treatmentFolderWithState;
            relativeModelPath = `${basePathPrefix}/${fileName}`;
          } else {
            // Regular treatment
            const folder = patientType === "CHILDREN" ? `child/${part}` : part;
            const fileName = `${toothIdInFilename}_${part}.glb`;
            relativeModelPath = `${folder}/${fileName}`;
          }

          const modelPath = `${BASE_URL}/${TREATMENTS_VERSION}/${relativeModelPath}`;

          try {
            console.log(
              `Loading model for tooth ${number}, part ${part}, patient type ${patientType}, view ${currentView}:`,
              modelPath, // This will now log the full URL
            );

            let gltf = modelCache.has(modelPath) // Cache key is now full URL
              ? {
                  scene: cloneModel(
                    modelCache.get(modelPath).scene, // Cache key is now full URL
                    currentView,
                  ),
                }
              : await new Promise((resolve, reject) => {
                  // modelPath is now a full URL
                  loaderRef.current.load(modelPath, resolve, undefined, reject);
                });

            if (!modelCache.has(modelPath)) {
              // Cache key is now full URL
              modelCache.set(modelPath, {
                // Cache key is now full URL
                scene: cloneModel(gltf.scene, currentView),
              });
              if (gltf.animations?.length) {
                animationCache.set(
                  modelPath, // Cache key is now full URL
                  gltf.animations.map((a) => a.clone()),
                );
              }
            }

            const animations = (animationCache.get(modelPath) || []).map(
              (anim) => createYPositionOnlyClip(anim),
            );

            if (!mountedRef.current) return;

            const child = gltf.scene.children[0];
            child.position.set(0, 0, 0);
            child.rotation.set(0, 0, 0);
            child.name = `tooth_${number}_${part}_${currentView}`;

            child.userData = child.userData || {};
            child.userData.createdAt = Date.now();
            child.userData.viewType = currentView;

            // First, collect all meshes
            const allMeshes = [];
            child.traverse((obj) => {
              if (obj.isMesh) {
                allMeshes.push(obj);
              }
            });

            // Log available morph targets for debugging if needed
            if (part === "Decay" || part === "Filling") {
              const morphTargetsFound = new Set();
              allMeshes.forEach((mesh) => {
                if (mesh.morphTargetDictionary) {
                  Object.keys(mesh.morphTargetDictionary).forEach((key) => {
                    morphTargetsFound.add(key);
                  });
                }
              });

              if (morphTargetsFound.size > 0) {
                // console.log(`Available morph targets for tooth ${number} (${part}):`, Array.from(morphTargetsFound));
              }
            }

            // Setup meshes and apply morph targets
            child.traverse((obj) => {
              if (obj.isMesh) {
                obj.userData = obj.userData || {};
                obj.userData.originalMaterial = obj.material.clone();
                obj.userData.number = parseInt(number, 10);
                obj.userData.type = "tooth";
                obj.userData.isInteractive = true;
                obj.userData.viewType = currentView;

                // Healthy tooth part visibility for "Default" model
                if (part === "Default") {
                  if (obj.name === "Root" || obj.name === "root") {
                    // Assuming mesh names are "Root" or "root"
                    obj.visible = determineHealthyPartVisibility(
                      toothData.treatments,
                      "root",
                    );
                  } else if (obj.name === "Crown" || obj.name === "crown") {
                    // Assuming mesh names are "Crown" or "crown"
                    obj.visible = determineHealthyPartVisibility(
                      toothData.treatments,
                      "crown",
                    );
                  }
                }

                // Default yellow color for all filling objects
                if (part === "Filling") {
                  // Clone the original material
                  const fillingMaterial = obj.material.clone();

                  // Set basic properties
                  fillingMaterial.color.set(0xffff00);
                  fillingMaterial.emissive.set(0x333300);

                  // Apply anti-z-fighting properties using our utility function
                  obj.material = applyAntiZFightingProperties(
                    fillingMaterial,
                    0, // Index 0 for default treatment
                    true, // Make it transparent
                  );

                  // Make all filling objects visible by default
                  // They will be controlled by morph targets and transparency
                  obj.visible = true;
                }

                // Apply transparency for all treatments
                if (toothData.treatments && toothData.treatments.length > 0) {
                  // Sort treatments by creation date (newest first)
                  const sortedTreatments = [...toothData.treatments].sort(
                    (a, b) => {
                      const dateA = new Date(a.created_at || 0);
                      const dateB = new Date(b.created_at || 0);
                      return dateB - dateA; // Newest first
                    },
                  );

                  console.log(
                    `[SkullJawTeeth] DEBUG: Tooth ${number}, Part ${part} sortedTreatments (inside obj.traverse):`,
                    JSON.parse(
                      JSON.stringify(
                        sortedTreatments.map((t) => ({
                          name: t.name,
                          created_at: t.created_at,
                          id: t.Id || t.id,
                        })),
                      ),
                    ),
                  );

                  // Find the treatment that corresponds to this part
                  const treatmentForPart = sortedTreatments.find(
                    (t) => t.name === part,
                  );

                  console.log(
                    `[SkullJawTeeth] PRE-DIAGNOSTIC CHECK: Tooth ${number}, Part ${part}, Mesh ${
                      obj.name
                    }. sortedTreatments.length: ${
                      sortedTreatments.length
                    }, treatmentForPart found: ${!!treatmentForPart}${
                      treatmentForPart ? `, Name: ${treatmentForPart.name}` : ""
                    }`,
                  );

                  // If we have multiple treatments and this part corresponds to one of them
                  if (sortedTreatments.length > 1 && treatmentForPart) {
                    // Find the index of this treatment in the sorted array
                    const index = sortedTreatments.indexOf(treatmentForPart);
                    console.log(
                      `[SkullJawTeeth] DEBUG: Tooth ${number}, Part ${part}, Treatment ${treatmentForPart.name}, Index for applyAntiZFightingProperties: ${index}`,
                    );

                    // Create a unique ID for the treatment
                    const baseId =
                      treatmentForPart.Id ||
                      treatmentForPart.id ||
                      `treatment_${index}`;
                    const treatmentId = `${baseId}_${index}`;

                    // Clone the material to avoid affecting other meshes
                    const treatmentMaterial = obj.material.clone();

                    console.log(
                      `[SkullJawTeeth] ABOUT TO CALL applyAntiZFightingProperties for Tooth ${number}, Part ${part}, index=${index}`,
                    );

                    // Apply anti-z-fighting properties
                    obj.material = applyAntiZFightingProperties(
                      treatmentMaterial,
                      index,
                      true, // Make it transparent
                    );

                    // Store the treatment information in the object's userData
                    obj.userData.treatmentId = treatmentId; // Store for future reference
                    obj.userData.baseId = baseId; // Store the original ID for reference
                    obj.userData.treatmentIndex = index; // Store the index for reference
                    obj.userData.treatmentName = treatmentForPart.name; // Store the name for reference

                    // Check if this treatment should be visible
                    const toothNumber = toothData.position_number || number;
                    const isVisible = getTreatmentVisibility(
                      toothNumber,
                      treatmentId,
                    );
                    obj.visible = isVisible;

                    // Set render order based on index (higher index = rendered first)
                    obj.renderOrder = 10 - index; // Negative so newer treatments render later
                    console.log(
                      `[SkullJawTeeth] DIAGNOSTIC: Tooth ${number}, Part ${part}, Mesh ${obj.name}, Treatment ${treatmentForPart.name} (Index: ${index}): Set obj.renderOrder to ${obj.renderOrder}. Material transparent: ${obj.material.transparent}, opacity: ${obj.material.opacity}, alphaTest: ${obj.material.alphaTest}`,
                    );
                  }
                }

                // Apply morph targets for decay and filling parts with exact matching
                if (
                  obj.morphTargetInfluences &&
                  obj.morphTargetDictionary &&
                  toothData.treatments &&
                  toothData.treatments.length > 0 &&
                  (part === "Decay" || part === "Filling")
                ) {
                  const morphTargetNames = Object.keys(
                    obj.morphTargetDictionary,
                  );

                  // Sort treatments by creation date (newest first)
                  const sortedTreatments = [...toothData.treatments].sort(
                    (a, b) => {
                      const dateA = new Date(a.created_at || 0);
                      const dateB = new Date(b.created_at || 0);
                      return dateB - dateA; // Newest first
                    },
                  );

                  // Process each treatment with different transparency levels
                  sortedTreatments.forEach((treatment) => {
                    // Skip treatments without surfaces
                    if (!treatment.surfaces) return;

                    // Process each surface in the treatment
                    Object.entries(treatment.surfaces).forEach(
                      ([surfaceName, surfaceData]) => {
                        if (surfaceData.decaySeverity !== undefined) {
                          // Use the imported findExactSurfaceMorphTarget function
                          const morphTarget = findExactSurfaceMorphTarget(
                            morphTargetNames,
                            surfaceName,
                            part,
                          );

                          if (morphTarget) {
                            const targetIndex =
                              obj.morphTargetDictionary[morphTarget];
                            obj.morphTargetInfluences[targetIndex] =
                              surfaceData.decaySeverity;

                            // For filling parts, make them visible
                            if (part === "Filling") {
                              obj.visible = true;
                            }
                          }
                        }
                      },
                    );
                  });
                }

                obj.material.side = THREE.DoubleSide;
                obj.castShadow = true;
                obj.receiveShadow = true;
              }

              if (obj.isMesh) {
                // Roo: Add check to ensure logging only happens for meshes
                // Log final material properties
                if (!obj.material) {
                  // This check is now somewhat redundant if obj.isMesh, but kept for robustness
                  console.error(
                    `[SkullJawTeeth] CRITICAL: Tooth ${number}, Part ${part}, Mesh ${obj.name} (UUID: ${obj.uuid}, Type: ${obj.type}): obj.material is undefined. Object details:`,
                    JSON.parse(
                      JSON.stringify(obj, (key, value) =>
                        value instanceof THREE.Object3D && key !== "children"
                          ? undefined
                          : value,
                      ),
                    ),
                  );
                }
                const mat = obj.material || {}; // Fallback is also less critical now but safe
                console.log(
                  `[SkullJawTeeth] DEBUG: Tooth ${number}, Part ${part}, Mesh ${obj.name}: Final Material Properties:`,
                  {
                    transparent: mat.transparent,
                    opacity: mat.opacity,
                    blending: mat.blending,
                    depthTest: mat.depthTest,
                    depthWrite: mat.depthWrite,
                    alphaTest: mat.alphaTest, // Log alphaTest
                    renderOrder: obj.renderOrder,
                    side: mat.side,
                    name: mat.name,
                    uuid: mat.uuid,
                    visible: obj.visible,
                  },
                );
                if (mat.transparent && !obj.visible) {
                  console.warn(
                    `[SkullJawTeeth] DEBUG: Tooth ${number}, Part ${part}, Mesh ${obj.name} (UUID: ${mat.uuid}) is transparent but NOT visible.`,
                  );
                }
                if (mat.transparent && obj.visible) {
                  console.log(
                    `[SkullJawTeeth] DEBUG: Tooth ${number}, Part ${part}, Mesh ${obj.name} (UUID: ${mat.uuid}) is transparent AND visible.`,
                  );
                }
                if (
                  !mat.transparent &&
                  mat.opacity < 1 &&
                  mat.opacity !== undefined
                ) {
                  // Check opacity !== undefined as it defaults to 1
                  console.warn(
                    `[SkullJawTeeth] DEBUG: Tooth ${number}, Part ${part}, Mesh ${obj.name} (UUID: ${mat.uuid}) has opacity (${mat.opacity}) < 1 but material.transparent is false.`,
                  );
                }
                if (obj.renderOrder === undefined || obj.renderOrder === 0) {
                  console.log(
                    `[SkullJawTeeth] DEBUG: Tooth ${number}, Part ${part}, Mesh ${obj.name} (UUID: ${mat.uuid}) has default renderOrder (0 or undefined).`,
                  );
                }
              } // Roo: Closes if (obj.isMesh) for logging
            });

            // Apply material coloring for exact material name matches - Decay
            if (
              part === "Decay" &&
              toothData.treatments &&
              toothData.treatments[0] &&
              toothData.treatments[0].surfaces
            ) {
              Object.entries(toothData.treatments[0].surfaces).forEach(
                ([surfaceName, surfaceData]) => {
                  if (surfaceData.decaySeverity > 0) {
                    // The specific material name we want to match exactly
                    const targetMaterialName = `decay_${surfaceName.toLowerCase()}`;

                    // Look through all meshes for exact match
                    allMeshes.forEach((mesh) => {
                      mesh.visbile = false;
                      const materialName = (
                        mesh.material?.name || ""
                      ).toLowerCase();

                      // Only color if the material name is EXACTLY "decay_[surfaceName]"
                      if (materialName === targetMaterialName) {
                        mesh.material.color.set(0x000000);
                        // console.log(`Applied black color to mesh with material '${materialName}'`);
                      }
                    });
                  }
                },
              );
            }

            // Apply material coloring for exact material name matches - Filling
            if (part === "Filling" && decaySurfaces.length > 0) {
              decaySurfaces.forEach((surfaceName) => {
                const surfaceData =
                  toothData.treatments[0].surfaces[surfaceName];
                if (surfaceData && surfaceData.decaySeverity > 0) {
                  // The corresponding filling material would be named similarly
                  const targetMaterialName = `filling_${surfaceName.toLowerCase()}`;

                  // Look through all meshes for exact match
                  allMeshes.forEach((mesh) => {
                    const materialName = (
                      mesh.material?.name || ""
                    ).toLowerCase();

                    // Only apply filling color if the material name is EXACTLY "filling_[surfaceName]"
                    if (materialName === targetMaterialName) {
                      mesh.material.color.set(0xffff00);
                      mesh.material.emissive.set(0x333300);
                      // console.log(`Applied filling color to mesh with material '${materialName}'`);
                    }
                  });
                }
              });
              console.log(
                `[SkullJawTeeth] DEBUG: Tooth ${number}, Adding part ${part} (mesh: ${child.name}, createdAt: ${child.userData.createdAt}) to pointer.`,
              );
            }

            // Get the treatment name for various operations
            const treatmentName =
              toothData.treatments && toothData.treatments.length > 0
                ? toothData.treatments[0].name
                : "Default";

            // Set the primary model based on the treatment name
            if (part === treatmentName) {
              primaryModel = child;
            }

            if (number >= 9 && number <= 24) {
              if (currentView === "skull") {
                child.scale.set(-child.scale.x, child.scale.y, -child.scale.z);
              } else if (currentView === "jaw") {
                child.scale.set(child.scale.x, -child.scale.y, child.scale.z);
                if (patientType === "CHILDREN") {
                  child.scale.set(
                    -child.scale.x,
                    -child.scale.y,
                    -child.scale.z,
                  );
                }
              } else if (currentView === "charting") {
                const originalScale = new Vector3();
                child.getWorldScale(originalScale);
                child.scale.set(1, 1, 1);
                child.updateMatrixWorld(true);
                child.scale.set(
                  -Math.abs(originalScale.x),
                  -Math.abs(originalScale.y),
                  -Math.abs(originalScale.z),
                );
              }
            }

            // Set rotation for Decay or Filling treatments
            if (treatmentName === "Decay" || treatmentName === "Filling") {
              child.rotation.set(-89.5, 0, 0);
            }

            pointer.add(child);

            if (animations.length) {
              processAnimations(
                parseInt(number, 10),
                animations,
                pointer,
                part,
              );
            }
          } catch (error) {
            console.error(`Error loading tooth ${number} ${part}:`, error);
          }
        }

        if (primaryModel) {
          internalTeethRef.current.set(parseInt(number, 10), primaryModel);

          if (teethRef && teethRef.current) {
            teethRef.current.set(parseInt(number, 10), primaryModel);
          }
        }

        markToothLoaded(number);
      },
      [
        mountedRef,
        internalTeethRef,
        teethRef,
        currentView,
        processAnimations,
        markToothLoaded,
        patientType,
        getTreatmentVisibility,
      ],
    );

    const validateScene = () => {
      if (!pointersRef?.current) return;

      const allMeshes = [];
      const duplicates = new Map();

      pointersRef.current.forEach((pointer, toothNumber) => {
        pointer.traverse((obj) => {
          if (obj.isMesh) {
            const meshInfo = {
              name: obj.name,
              uuid: obj.uuid,
              tooth: toothNumber,
              parent: obj.parent?.name,
            };

            if (allMeshes.some((m) => m.uuid === obj.uuid)) {
              duplicates.set(obj.uuid, meshInfo);
            }
            allMeshes.push(meshInfo);
          }
        });
      });
    };

    useEffect(() => {
      if (
        loadingTeeth.size > 0 &&
        loadingTeeth.size === totalTeethToLoadRef.current &&
        isLoading
      ) {
        setIsLoading(false);
      }
    }, [loadingTeeth, isLoading, currentView]);

    useEffect(() => {
      mountedRef.current = true;

      const teethKey = Object.keys(patientTeeth).sort().join(",");
      const instanceKey = `${currentView}-${teethKey}`;

      if (isInitializedRef.current === instanceKey) {
        return;
      }

      // Always clean up existing teeth first
      cleanupTeeth();

      // Update the instance key
      isInitializedRef.current = instanceKey;

      // Reset loading state
      setLoadingTeeth(new Map());

      // Only load new teeth if there are any
      if (pointersRef?.current && Object.keys(patientTeeth).length > 0) {
        const teethToLoad = Object.keys(patientTeeth);
        totalTeethToLoadRef.current = teethToLoad.length;

        setIsLoading(true);

        // Add a small delay between loading each tooth to prevent overwhelming the GPU
        teethToLoad.forEach((number, index) => {
          setTimeout(() => {
            if (!mountedRef.current) return;
            const pointer = pointersRef.current?.get(parseInt(number, 10));
            if (pointer) loadToothModel(number, patientTeeth[number], pointer);
          }, index * 50); // 50ms delay between each tooth
        });
      } else {
        // If there are no teeth to load, make sure we're not in loading state
        totalTeethToLoadRef.current = 0;
        setIsLoading(false);
      }

      return () => {
        mountedRef.current = false;
      };
    }, [patientTeeth, pointersRef, currentView, cleanupTeeth, loadToothModel]);

    React.useImperativeHandle(ref, () => ({
      playAnimations,
      pauseAnimations,
      resetAnimations,
      validateScene,
      isLoading: () => isLoading,
      loadedTeethCount: () => loadingTeeth.size,
      totalTeethCount: () => totalTeethToLoadRef.current,
      // Add setToothMorphTargets for backward compatibility
      setToothMorphTargets: () => {
        // This function is kept for backward compatibility
        // The component now handles morph targets internally based on the patientTeeth data
        // No action needed as morph targets are now applied directly in loadToothModel
        return true;
      },
    }));

    return null;
  },
);

SkullJawTeeth.displayName = "SkullJawTeeth";
export default SkullJawTeeth;
