import { useRef, useState, useEffect, useCallback } from 'react';
import * as THREE from 'three';
import { createHighlightMaterial } from '../utils/modelUtils';

export const useMouseInteractions = ({
  camera,
  gl,
  teethRef,
  setHoveredTooth,
  setSelectedTooth,
  openRightClickModal
}) => {
  const raycasterRef = useRef(new THREE.Raycaster());
  const mouseRef = useRef(new THREE.Vector2());
  const prevHoveredRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const lastEventTimeRef = useRef(0);
  const mountedRef = useRef(true);
  const highlightedMeshesRef = useRef(new Set());

  // Get the current view type from the first tooth (if any)
  const getViewType = useCallback(() => {
    if (!teethRef?.current?.size) return "unknown";
    const firstTooth = teethRef.current.values().next().value;
    return firstTooth?.userData?.viewType || "unknown";
  }, [teethRef]);

  // Reset all highlighted materials when unmounting
  const resetAllHighlightedMeshes = useCallback(() => {
    if (highlightedMeshesRef.current.size > 0) {
      highlightedMeshesRef.current.forEach(mesh => {
        if (mesh.userData.originalMaterial) {
          mesh.material = mesh.userData.originalMaterial.clone();
        }
      });

      highlightedMeshesRef.current.clear();
    }
  }, []);

  // Get all interactive objects from the teeth
  const getInteractiveObjects = useCallback(() => {
    if (!teethRef || !teethRef.current) {
      return [];
    }

    const objects = [];

    // Skip logging on every mouse move
    const shouldLog = Date.now() - lastEventTimeRef.current > 1000;
    if (shouldLog) {
      lastEventTimeRef.current = Date.now();
    }

    teethRef.current.forEach((tooth) => {
      if (tooth) {
        tooth.traverse((child) => {
          if (child.isMesh && child.userData.isInteractive) {
            objects.push(child);
          }
        });
      }
    });

    return objects;
  }, [teethRef]);

  // Check if a tooth should skip material changes
  const shouldSkipMaterialChange = useCallback((toothNumber) => {
    const tooth = teethRef.current.get(toothNumber);
    if (!tooth) return false;

    // Check if the tooth name contains "Decay" or "filling"
    const toothName = tooth.name || '';
    return toothName.includes("Decay") || toothName.includes("Filling");
  }, [teethRef]);

  // Handle hover state changes
  const handleHover = useCallback((toothNumber) => {
    if (!mountedRef.current) return;
    if (prevHoveredRef.current === toothNumber) return;

    const viewType = getViewType();

    // Reset previous hover
    if (prevHoveredRef.current) {
      const prevTooth = teethRef.current.get(prevHoveredRef.current);
      const skipPrevMaterialChange = shouldSkipMaterialChange(prevHoveredRef.current);

      if (prevTooth) {
        prevTooth.traverse((child) => {
          // Only restore material if it's not a Decay or filling tooth
          if (child.isMesh && child.userData.originalMaterial &&
              highlightedMeshesRef.current.has(child) && !skipPrevMaterialChange) {
            try {
              // Create a new instance of the material to avoid sharing
              child.material = child.userData.originalMaterial.clone();
              highlightedMeshesRef.current.delete(child);
            } catch (e) {
              console.error(`[${viewType}] MouseInteractions: Error restoring original material:`, e);
            }
          }
        });
      }
    }

    // Set new hover
    if (toothNumber) {
      const tooth = teethRef.current.get(toothNumber);
      const skipMaterialChange = shouldSkipMaterialChange(toothNumber);

      if (tooth) {
        tooth.traverse((child) => {
          if (child.isMesh) {
            // Skip material change for Decay or filling teeth but still track for hover state
            if (!skipMaterialChange) {
              if (!child.userData.originalMaterial) {
                // Store a clone of the original material
                child.userData.originalMaterial = child.material.clone();
              }
              // Set highlight material - create a new one each time
              child.material = createHighlightMaterial();
              // Add to tracked set
              highlightedMeshesRef.current.add(child);
            }
          }
        });
      }
    }

    prevHoveredRef.current = toothNumber;
    // Always update hovered tooth state regardless of material change
    setHoveredTooth(toothNumber);


  }, [getViewType, setHoveredTooth, shouldSkipMaterialChange, teethRef]);

  // Handle mouse move events
  const handleMouseMove = useCallback((event) => {
    if (!mountedRef.current || !isInitialized) return;

    const rect = gl.domElement.getBoundingClientRect();
    mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    raycasterRef.current.setFromCamera(mouseRef.current, camera);

    const interactiveObjects = getInteractiveObjects();



    if (interactiveObjects.length === 0) return;

    const intersects = raycasterRef.current.intersectObjects(interactiveObjects, true);

    if (intersects.length > 0) {
      const toothNumber = parseInt(intersects[0].object.userData.number);
      if (toothNumber) {
        handleHover(toothNumber);
      }
    } else {
      handleHover(null);
    }
  }, [camera, getInteractiveObjects, gl, handleHover, isInitialized]);

  // Handle click events
  const handleClick = useCallback((event) => {
    if (!mountedRef.current || !isInitialized) return;

    const rect = gl.domElement.getBoundingClientRect();
    mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    raycasterRef.current.setFromCamera(mouseRef.current, camera);

    const interactiveObjects = getInteractiveObjects();
    if (interactiveObjects.length === 0) return;

    const intersects = raycasterRef.current.intersectObjects(interactiveObjects, true);

    if (intersects.length > 0) {
      const toothNumber = parseInt(intersects[0].object.userData.number);
      if (event.button === 0) {
        // Left click
        // We still want to handle selection for all teeth, even Decay/filling ones
        setSelectedTooth((prev) => (prev === toothNumber ? null : toothNumber));
        // console.log('Left Click on tooth:', toothNumber)
      } else if (event.button === 2) {
        // Right click
        event.preventDefault(); // Prevent context menu
        const modalWidth = 300;
        const modalHeight = 150;
        const padding = 35;

        const x = Math.min(window.innerWidth - modalWidth - padding, event.clientX);
        const y = Math.min(window.innerHeight - modalHeight - padding, event.clientY);
        openRightClickModal({ tooth: toothNumber, cursor: { x: x, y: y } });
        // console.log('Right click on tooth:', toothNumber);
      }
    } else {
      setSelectedTooth(null);
    }
  }, [getInteractiveObjects, gl, isInitialized, setSelectedTooth, camera]);

  // Initialize when teeth are available
  useEffect(() => {
    if (teethRef?.current?.size > 0 && !isInitialized) {
      setIsInitialized(true);
    }
  }, [teethRef, isInitialized]);

  // Watch for changes in the teeth ref size with interval polling
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (teethRef?.current?.size > 0 && !isInitialized) {
        setIsInitialized(true);
        clearInterval(intervalId);
      }
    }, 500);

    return () => {
      clearInterval(intervalId);
    };
  }, [teethRef, isInitialized]);

  // Attach event listeners
  useEffect(() => {
    if (!isInitialized || !gl) return;

    const viewType = getViewType();

    // Validate that we have teeth refs before setting up event handlers
    if (!teethRef || !teethRef.current) {
      console.error(`[${viewType}] MouseInteractions: Missing teeth ref`);
      return;
    }

    const canvas = gl.domElement;
    canvas.style.touchAction = "none";

    // Use throttled versions of event handlers for better performance
    let lastMoveTime = 0;
    const throttleTime = 50; // Use original throttle time for better responsiveness

    const throttledMouseMove = (event) => {
      const now = Date.now();
      if (now - lastMoveTime > throttleTime) {
        lastMoveTime = now;
        handleMouseMove(event);
      }
    };

    canvas.addEventListener("mousemove", throttledMouseMove);
    canvas.addEventListener("mousedown", handleClick);
    canvas.addEventListener('contextmenu', e => e.preventDefault());

    return () => {
      canvas.removeEventListener("mousemove", throttledMouseMove);
      canvas.removeEventListener("click", handleClick);
      // Reset hover state
      handleHover(null);
    };
  }, [gl, teethRef, isInitialized, getViewType, handleClick, handleHover, handleMouseMove]);

  // Cleanup on unmount
  useEffect(() => {
    mountedRef.current = true;

    return () => {
      mountedRef.current = false;

      // Reset all highlighted materials
      resetAllHighlightedMeshes();

      // Reset hover state on unmount
      setHoveredTooth(null);
    };
  }, [resetAllHighlightedMeshes, setHoveredTooth]);

  return {
    isInitialized,
    resetAllHighlightedMeshes
  };
};
