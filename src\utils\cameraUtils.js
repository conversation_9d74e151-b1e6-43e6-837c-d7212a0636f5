import {
  CAMERA_CONFIG,
  <PERSON><PERSON><PERSON>_CAMERA_CONFIG,
  GRID_CAMERA_CONFIG,
  SINGLE_TREATMENT_CAMERA_CONFIG,
  CONTROLS_CONFIG,
  JAW_CONTROLS_CONFIG,
  GRID_CONTROLS_CONFIG,
  SINGLE_TREATMENT_CONTROLS_CONFIG
} from "../constants/camera_config";

/**
 * Reset the camera to its default position for a specific view
 * @param {Object} camera - The Three.js camera
 * @param {Object} controls - The OrbitControls instance
 * @param {string} viewMode - The current view mode (skull, jaw, charting, single_treatment)
 */
export const resetCameraToDefault = (camera, controls, viewMode) => {
  if (!camera || !controls) {
    console.warn('Cannot reset camera: camera or controls not provided');
    return;
  }

  // Get the configuration for the current view
  const viewConfigs = {
    skull: {
      camera: CAMERA_CONFIG,
      controls: CONTROLS_CONFIG,
    },
    jaw: {
      camera: JAW_CAMERA_CONFIG,
      controls: JA<PERSON>_CONTROLS_CONFIG,
    },
    charting: {
      camera: GRID_CAMERA_CONFIG,
      controls: GRID_CONTROLS_CONFIG,
    },
    single_treatment: {
      camera: SINGLE_TREATMENT_CAMERA_CONFIG,
      controls: SINGLE_TREATMENT_CONTROLS_CONFIG,
    }
  };

  const config = viewConfigs[viewMode] || viewConfigs.skull;

  // Reset camera position and target
  camera.position.set(...config.camera.position);
  camera.fov = config.camera.fov;
  
  // Reset controls
  controls.target.set(...config.controls.target);
  
  // Update the camera
  camera.lookAt(...config.controls.target);
  camera.updateProjectionMatrix();
  controls.update();
  
  console.log(`Camera reset to default for ${viewMode} view`);
};
