import { Html } from "@react-three/drei";
import { Vector3 } from "three";
import { useThree, useFrame } from "@react-three/fiber";
import { useState, useRef, useEffect } from "react";
import "./tooth_tag.css";

const getToothData = (toothNumber) => {
  if (!toothNumber) return null;

  return {
    number: toothNumber,
    name: "<PERSON><PERSON>",
    status: "healthy",
    lastTreatment: "2023-10-01",
    history: ["Filling (2022)", "Cleaning (2021)"],
    notes: "No issues detected. Regular checkup recommended.",
  };
};

export function ToothTag({ target, isHovered, isSelected }) {
  const { camera } = useThree();
  const [position] = useState(() => new Vector3());
  const [scaleFactor, setScaleFactor] = useState(1);
  const htmlRef = useRef(null);
  const [toothData, setToothData] = useState(null);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (target?.userData?.number) {
      setToothData(getToothData(target.userData.number));
      setVisible(true);
    } else {
      setVisible(false);
    }
  }, [target]);

  useFrame(() => {
    if (!target || !visible) return;

    try {
      // Get current world position of the tooth
      target.getWorldPosition(position);

      // Calculate scale based on distance to camera
      const distanceToCamera = position.distanceTo(camera.position);
      const fov = camera.fov * (Math.PI / 180);
      const baseScale = Math.tan(fov / 2) * 0.2; // Reduced from 0.4 to 0.2
      setScaleFactor(Math.min(Math.max(baseScale * 25, 0.2), 0.5)); // Adjusted scale range
    } catch (error) {
      console.error("Error in ToothTag frame update:", error);
    }
  });

  if (!visible || !toothData) return null;

  return (
    <Html
     ref={htmlRef}
  center
  className={`tooth-tag ${isSelected ? "selected" : ""} ${isHovered ? "hovered" : ""}`}
  distanceFactor={window.innerWidth < 500 ? scaleFactor * 0.2 : scaleFactor* 0.4}
  position={position}
    >
      <div className="tooth-tag-content">
        <div className="tooth-tag-header">
          <div className="tooth-number">
            <span>#{toothData.number}</span>
            <div
              className="status-indicator"
              style={{
                backgroundColor:
                  toothData.status === "healthy"
                    ? "#4ade80"
                    : toothData.status === "treated"
                    ? "#60a5fa"
                    : toothData.status === "needs-treatment"
                    ? "#f87171"
                    : "#94a3b8",
              }}
            />
          </div>
          <span
            className="tooth-status"
            style={{
              color:
                toothData.status === "healthy"
                  ? "#16a34a"
                  : toothData.status === "treated"
                  ? "#2563eb"
                  : toothData.status === "needs-treatment"
                  ? "#dc2626"
                  : "#64748b",
            }}
          >
            {toothData.status.replace("-", " ")}
          </span>
        </div>

        <div className="tooth-name">{toothData.name}</div>

        {isSelected && (
          <div className="tooth-details">
            <div className="details-grid">
              <div className="details-column">
                <div className="detail-card">
                  <span className="detail-label">Last Treatment</span>
                  <span className="detail-value">{toothData.lastTreatment}</span>
                </div>
                <div className="detail-card">
                  <span className="detail-label">Treatment History</span>
                  <div className="history-list">
                    {toothData.history.map((item, index) => (
                      <div key={index} className="history-item">
                        <div className="history-bullet" />
                        <span>{item}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="details-column">
                <div className="video-placeholder">
                  <div className="video-content">
                    <div className="video-icon" />
                    <p className="video-title">Treatment Video</p>
                    <p className="video-subtitle">
                      Click to play procedure walkthrough
                    </p>
                  </div>
                </div>
                <div className="detail-card">
                  <h4 className="detail-label">Treatment Notes</h4>
                  <p className="detail-value">{toothData.notes}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="tooth-action">
          {isSelected
            ? "Click on background to close"
            : "Click on the tooth for more information"}
        </div>
      </div>
    </Html>
  );
}