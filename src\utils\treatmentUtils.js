import { findExactSurfaceMorphTarget } from "./modelUtils";
import { applyAntiZFightingProperties } from "./materialUtils";

/**
 * Finds a specific treatment model part within a tooth's main group.
 * @param {THREE.Group} toothGroup - The main group for the tooth.
 * @param {number} toothNumber - The number of the tooth.
 * @param {string} treatmentName - The name of the treatment (model part name).
 * @param {string} viewType - The current view type ("skull", "jaw", "single_treatment").
 * @returns {THREE.Object3D | null} The found model part or null.
 */
function findTreatmentModelPart(
  toothGroup,
  toothNumber,
  treatmentName,
  viewType,
) {
  if (!toothGroup) return null;
  const partName = `tooth_${toothNumber}_${treatmentName}_${viewType}`;
  return toothGroup.getObjectByName(partName);
}

/**
 * Applies visual aspects of a single treatment to its 3D model part(s).
 * @param {THREE.Group} toothGroup - The main THREE.Group containing all loaded parts for a single tooth.
 * @param {object} toothData - The data object for the specific tooth.
 * @param {object} treatmentToApply - The specific treatment object to apply.
 * @param {Array<object>} allTreatmentsOnTooth - Sorted array of all treatments for this tooth (newest first).
 * @param {string} viewType - Current view type ("skull", "jaw", "single_treatment").
 * @param {function} getTreatmentVisibility - Function from TeethContext to check visibility.
 */
export const applyTreatmentVisuals = (
  toothGroup,
  toothData,
  treatmentToApply,
  allTreatmentsOnTooth,
  viewType,
  getTreatmentVisibility,
) => {
  if (!treatmentToApply || !toothGroup) {
    return;
  }

  const toothNumber =
    toothData.position_number ||
    parseInt(toothData.position?.match(/\d+/)?.[0] || 0, 10);
  if (!toothNumber) {
    console.warn(
      "Could not determine tooth number for treatment application.",
      toothData,
    );
    return;
  }

  const treatmentIndex = allTreatmentsOnTooth.findIndex(
    (t) =>
      (t.Id && t.Id === treatmentToApply.Id) ||
      (t.id && t.id === treatmentToApply.id) ||
      t === treatmentToApply,
  );

  if (treatmentIndex === -1) {
    console.warn(
      `Treatment to apply not found in allTreatmentsOnTooth list. ID: ${
        treatmentToApply.Id || treatmentToApply.id
      }`,
      treatmentToApply,
      allTreatmentsOnTooth,
    );
    return;
  }

  // Ensure treatmentId is a string and does not contain undefined
  const baseId = String(
    treatmentToApply.Id || treatmentToApply.id || `generated_${treatmentIndex}`,
  );
  const treatmentIdForVisibility = `${baseId}_${treatmentIndex}`;

  const isVisible = getTreatmentVisibility(
    toothNumber,
    treatmentIdForVisibility,
  );

  const targetModelPart = findTreatmentModelPart(
    toothGroup,
    toothNumber,
    treatmentToApply.name,
    viewType,
  );

  if (!targetModelPart) {
    // Allow conceptual treatments like "Missing" or if "Default" is the only part.
    if (
      treatmentToApply.name !== "Default" &&
      !treatmentToApply.missing_tooth_indicator
    ) {
      // console.warn(`Model part for treatment "${treatmentToApply.name}" not found in toothGroup for tooth ${toothNumber}. View: ${viewType}`);
    }
    // If it's the default part and no other specific treatment part is found,
    // it might still need its visibility and layering set.
    if (treatmentToApply.name === "Default") {
      const defaultPart = findTreatmentModelPart(
        toothGroup,
        toothNumber,
        "Default",
        viewType,
      );
      if (defaultPart) {
        defaultPart.visible = isVisible;
        if (isVisible) {
          defaultPart.traverse((obj) => {
            if (obj.isMesh) {
              if (!obj.userData.originalMaterial)
                obj.userData.originalMaterial = obj.material.clone();
              obj.material = applyAntiZFightingProperties(
                obj.userData.originalMaterial.clone(),
                treatmentIndex,
                false,
              ); // Default usually not transparent
              obj.renderOrder = -treatmentIndex;
            }
          });
        }
      }
    }
    return;
  }

  targetModelPart.visible = isVisible;
  if (!isVisible) {
    return;
  }
  targetModelPart.renderOrder = -treatmentIndex;

  if (treatmentToApply.name === "Decay") {
    targetModelPart.traverse((mesh) => {
      if (mesh.isMesh) {
        if (!mesh.userData.originalMaterial)
          mesh.userData.originalMaterial = mesh.material.clone();

        // Morph Targets for Decay
        if (
          mesh.morphTargetInfluences &&
          mesh.morphTargetDictionary &&
          treatmentToApply.surfaces
        ) {
          mesh.morphTargetInfluences.fill(0); // Reset
          const morphTargetNames = Object.keys(mesh.morphTargetDictionary);
          Object.entries(treatmentToApply.surfaces).forEach(
            ([surfaceName, surfaceData]) => {
              if (surfaceData.decaySeverity !== undefined) {
                const morphTarget = findExactSurfaceMorphTarget(
                  morphTargetNames,
                  surfaceName,
                  "Decay",
                );
                if (
                  morphTarget &&
                  Object.prototype.hasOwnProperty.call(
                    mesh.morphTargetDictionary,
                    morphTarget,
                  )
                ) {
                  const morphIdx = mesh.morphTargetDictionary[morphTarget];
                  if (
                    morphIdx !== undefined &&
                    morphIdx < mesh.morphTargetInfluences.length
                  ) {
                    mesh.morphTargetInfluences[morphIdx] =
                      surfaceData.decaySeverity;
                  }
                }
              }
            },
          );
        }

        // Material Coloring for Decay (specific sub-meshes)
        let appliedSpecificMaterial = false;
        if (treatmentToApply.surfaces) {
          Object.entries(treatmentToApply.surfaces).forEach(
            ([surfaceName, surfaceData]) => {
              if (surfaceData.decaySeverity > 0) {
                const targetMaterialName = `decay_${surfaceName.toLowerCase()}`;
                if (
                  (mesh.material.name || "").toLowerCase() ===
                  targetMaterialName
                ) {
                  const decayMaterial = mesh.userData.originalMaterial.clone();
                  decayMaterial.color.set(0x000000); // Black
                  mesh.material = applyAntiZFightingProperties(
                    decayMaterial,
                    treatmentIndex,
                    true,
                  );
                  appliedSpecificMaterial = true;
                }
              }
            },
          );
        }
        // If no specific surface material was applied, apply to the whole mesh material
        if (!appliedSpecificMaterial) {
          mesh.material = applyAntiZFightingProperties(
            mesh.userData.originalMaterial.clone(),
            treatmentIndex,
            true,
          );
        }
      }
    });
  } else if (treatmentToApply.name === "Filling") {
    targetModelPart.traverse((mesh) => {
      if (mesh.isMesh) {
        if (!mesh.userData.originalMaterial)
          mesh.userData.originalMaterial = mesh.material.clone();

        // Morph Targets for Filling
        if (
          mesh.morphTargetInfluences &&
          mesh.morphTargetDictionary &&
          treatmentToApply.surfaces
        ) {
          mesh.morphTargetInfluences.fill(0); // Reset
          const morphTargetNames = Object.keys(mesh.morphTargetDictionary);
          Object.entries(treatmentToApply.surfaces).forEach(
            ([surfaceName, surfaceData]) => {
              if (surfaceData.decaySeverity !== undefined) {
                // Fillings are shaped by decay severity
                const morphTarget = findExactSurfaceMorphTarget(
                  morphTargetNames,
                  surfaceName,
                  "Filling",
                );
                if (
                  morphTarget &&
                  Object.prototype.hasOwnProperty.call(
                    mesh.morphTargetDictionary,
                    morphTarget,
                  )
                ) {
                  const morphIdx = mesh.morphTargetDictionary[morphTarget];
                  if (
                    morphIdx !== undefined &&
                    morphIdx < mesh.morphTargetInfluences.length
                  ) {
                    mesh.morphTargetInfluences[morphIdx] =
                      surfaceData.decaySeverity;
                  }
                }
              }
            },
          );
        }

        // Standard Material for Filling
        const fillingMaterial = mesh.userData.originalMaterial.clone();
        fillingMaterial.color.set(0xffff00); // Yellow
        fillingMaterial.emissive.set(0x333300);
        mesh.material = applyAntiZFightingProperties(
          fillingMaterial,
          treatmentIndex,
          true,
        );
      }
    });
  } else {
    // Simple Treatments (e.g., Crown, Implant, etc.)
    targetModelPart.traverse((mesh) => {
      if (mesh.isMesh) {
        if (!mesh.userData.originalMaterial)
          mesh.userData.originalMaterial = mesh.material.clone();
        mesh.material = applyAntiZFightingProperties(
          mesh.userData.originalMaterial.clone(),
          treatmentIndex,
          true,
        );
      }
    });
  }
};
