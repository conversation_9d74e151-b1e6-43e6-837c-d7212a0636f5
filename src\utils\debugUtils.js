/**
 * Debug utility functions for the application
 */

/**
 * Logs the visibility state of all treatments
 * @param {Object} treatmentVisibility - The treatment visibility state object
 */
export const logTreatmentVisibility = (treatmentVisibility) => {
  console.log('Current treatment visibility state:');

  // First try to get the latest state from the getter function
  if (typeof window !== 'undefined' && window.__GET_TEETH_CONTEXT__) {
    try {
      const latestContext = window.__GET_TEETH_CONTEXT__();
      if (latestContext && latestContext.treatmentVisibility) {
        console.log('From context getter:');
        console.log(JSON.stringify(latestContext.treatmentVisibility, null, 2));
        return;
      }
    } catch (e) {
      console.error('Error accessing context getter:', e);
    }
  }

  // Then try the passed parameter
  if (treatmentVisibility !== undefined) {
    console.log('From parameter:');
    console.log(JSON.stringify(treatmentVisibility, null, 2));
    return;
  }

  // Finally try the static context
  try {
    const teethContext = window.__TEETH_CONTEXT__;
    if (teethContext && teethContext.treatmentVisibility) {
      console.log('From static context:');
      console.log(JSON.stringify(teethContext.treatmentVisibility, null, 2));
    } else {
      console.log('Could not find treatmentVisibility in any source');
    }
  } catch (e) {
    console.error('Error accessing static context:', e);
  }
};

/**
 * Logs all treatments in a model with their visibility status
 * @param {THREE.Object3D} model - The model to inspect
 */
export const logModelTreatments = (model) => {
  if (!model) {
    console.log('No model provided');
    return;
  }

  console.log('Model treatments:');
  const treatments = [];

  model.traverse(obj => {
    if (obj.isMesh && obj.userData && obj.userData.treatmentId) {
      treatments.push({
        name: obj.name,
        treatmentId: obj.userData.treatmentId,
        treatmentIndex: obj.userData.treatmentIndex,
        treatmentName: obj.userData.treatmentName,
        visible: obj.visible
      });
    }
  });

  // Group by treatment ID
  const groupedTreatments = {};
  treatments.forEach(treatment => {
    if (!groupedTreatments[treatment.treatmentId]) {
      groupedTreatments[treatment.treatmentId] = [];
    }
    groupedTreatments[treatment.treatmentId].push(treatment);
  });

  console.log(JSON.stringify(groupedTreatments, null, 2));
};

/**
 * Adds a debug button to the UI
 * @param {Function} onClick - Function to call when the button is clicked
 */
export const addDebugButton = (onClick) => {
  // Create a button element
  const button = document.createElement('button');
  button.textContent = 'Debug Treatments';
  button.style.position = 'fixed';
  button.style.bottom = '10px';
  button.style.left = '10px';
  button.style.zIndex = '9999';
  button.style.padding = '8px 16px';
  button.style.backgroundColor = '#f44336';
  button.style.color = 'white';
  button.style.border = 'none';
  button.style.borderRadius = '4px';
  button.style.cursor = 'pointer';

  // Add click handler
  button.addEventListener('click', onClick);

  // Add to the document
  document.body.appendChild(button);

  return button;
};
