/**
 * Utility functions for handling message communication between UPOD and the iframe
 * for patient teeth data.
 */

/**
 * Initialize the message event listener for UPOD messages
 * @param {Object} teethContext - The teeth context object from useTeeth()
 * @returns {Function} - Function to remove the event listener
 */
export const initializeMessageListener = (teethContext) => {
  const {
    setPatientTeeth,
    clearAllTeeth,
    setEraserToolActive,
    setSelectedTreatment
  } = teethContext;

  const handleMessage = (event) => {
    // Ensure the message is from a trusted source if needed
    // if (event.origin !== "https://trusted-domain.com") return;

    const { data } = event;

    if (!data || !data.type) return;



    switch (data.type) {
      case 'initialize_teeth':
        handleInitializeTeeth(data.teeth, setPatientTeeth);
        break;

      case 'clear_teeth':
        handleClearTeeth(clearAllTeeth);
        break;

      case 'treatment_selected':
        handleTreatmentSelected(data.treatment, setSelectedTreatment);
        break;

      case 'charted_treatment_saved':
        handleChartedTreatmentSaved(data.ctId, teethContext);
        break;

      case 'eraser_state_change':
        handleEraserStateChange(data.active, setEraserToolActive);
        break;

      case 'charted_treatment_deleted':
        handleChartedTreatmentDeleted(data.ctId, teethContext);
        break;

      case 'charted_treatment_removed':
        handleChartedTreatmentRemoved(data.ctId, data.position, teethContext);
        break;

      case 'tooth_removed':
        handleToothRemoved(data.position, teethContext);
        break;

      case 'mixed_dentition_saved':
        handleMixedDentitionSaved(data.position, data.active, teethContext);
        break;

      case 'bridge_treatment_saved':
        handleBridgeTreatmentSaved(data.ctId, teethContext);
        break;

      case 'charted_treatment_completed':
        handleChartedTreatmentCompleted(data.ctId, data.position, teethContext);
        break;

      case 'initialize_single_tooth':
        handleInitializeSingleTooth(data.tooth, teethContext);
        break;

      case 'watch_tooth':
        handleWatchTooth(data.position, data.watched, teethContext);
        break;

      default:

    }
  };

  // Add event listener
  window.addEventListener('message', handleMessage);

  // Return function to remove event listener
  return () => {
    window.removeEventListener('message', handleMessage);
  };
};

/**
 * Send a message to the parent window (UPOD)
 * @param {String} type - The message type
 * @param {Object} data - The message data
 * @returns {Object} - The response object with status and message
 */
export const sendMessage = (type, data = {}) => {
  try {
    const message = {
      type,
      ...data
    };

    window.parent.postMessage(message, '*');

    return {
      status: 200,
      message: type
    };
  } catch (error) {
    console.error('Error sending message:', error);

    return {
      status: 422,
      message: error.message
    };
  }
};

// Handler functions for each message type

/**
 * Handle initialize_teeth message
 * @param {Array} teeth - Array of teeth objects
 * @param {Function} setPatientTeeth - Function to set patient teeth
 */
const handleInitializeTeeth = (teeth, setPatientTeeth) => {
  // Convert array of teeth to object with tooth number as key
  const teethObject = teeth.reduce((acc, tooth) => {
    // Extract position number from position name (e.g., "UL8" -> "8")
    const positionNumber = tooth.position_number || extractPositionNumber(tooth.position);

    if (positionNumber) {
      // Ensure treatments have all required fields
      const processedTreatments = (tooth.treatments || []).map(treatment => ({
        Id: treatment.Id || treatment.id || Math.floor(Math.random() * 10000).toString(),
        ctid: treatment.ctid || treatment.ctId || "",
        completed: treatment.completed || false,
        name: treatment.name || "Default",
        full_tooth_treatment: treatment.full_tooth_treatment || false,
        patient_treatment: treatment.patient_treatment || false,
        remove_tooth_when_completed: treatment.remove_tooth_when_completed || false,
        remove_treatment_when_completed: treatment.remove_treatment_when_completed || false,
        bridge_treatment: treatment.bridge_treatment || false,
        missing_tooth_indicator: treatment.missing_tooth_indicator || false,
        mixed_dentition: treatment.mixed_dentition || false,
        created_at: treatment.created_at || new Date().toISOString(),
        completed_at: treatment.completed_at || null,
        surfaces: treatment.surfaces || {}
      }));

      acc[positionNumber] = {
        position: tooth.position,
        position_number: parseInt(positionNumber, 10),
        status: tooth.status || 'healthy',
        lastTreatment: tooth.lastTreatment || new Date().toISOString().split('T')[0],
        notes: tooth.notes || '',
        marked_as_missing: tooth.marked_as_missing || false,
        marked_as_watched: tooth.marked_as_watched|| false,
        treatments: processedTreatments
      };
    }

    return acc;
  }, {
    // Add patient type to the teeth object
    patientType: teeth.patientType || "ADULT" // Default to ADULT if not specified
  });

  setPatientTeeth(teethObject);

  // Send confirmation message
  sendTeethInitialized();
};

/**
 * Handle clear_teeth message
 * @param {Function} clearAllTeeth - Function to clear all teeth
 */
const handleClearTeeth = (clearAllTeeth) => {
  clearAllTeeth();

  // Send confirmation message
  sendMessage('teeth_cleared');
};

/**
 * Handle treatment_selected message
 * @param {Object} treatment - The treatment object
 * @param {Function} setSelectedTreatment - Function to set selected treatment
 */
const handleTreatmentSelected = (treatment, setSelectedTreatment) => {
  setSelectedTreatment(treatment);
};

/**
 * Handle charted_treatment_saved message
 * @param {String} ctId - The charted treatment ID
 * @param {Object} teethContext - The teeth context object
 */
const handleChartedTreatmentSaved = (ctId, teethContext) => {
  const { selectedTooth, selectedTreatment, patientTeeth, updateToothInfo } = teethContext;

  if (!selectedTooth || !selectedTreatment) return;

  const tooth = patientTeeth[selectedTooth];

  if (!tooth) return;

  // Find the last treatment without a ctId (the one we just added)
  const treatments = [...tooth.treatments];
  const lastTreatmentIndex = treatments.length - 1;

  if (lastTreatmentIndex >= 0) {
    treatments[lastTreatmentIndex] = {
      ...treatments[lastTreatmentIndex],
      ctid: ctId // Use ctid to match the expected format
    };

    updateToothInfo(selectedTooth, { treatments });
  }
};

/**
 * Handle eraser_state_change message
 * @param {Boolean} active - Whether the eraser is active
 * @param {Function} setEraserToolActive - Function to set eraser tool active state
 */
const handleEraserStateChange = (active, setEraserToolActive) => {
  setEraserToolActive(active);
};

/**
 * Handle charted_treatment_deleted message
 * @param {String} ctId - The charted treatment ID
 * @param {Object} teethContext - The teeth context object
 */
const handleChartedTreatmentDeleted = (ctId, teethContext) => {
  const { patientTeeth, setPatientTeeth } = teethContext;

  // Find the tooth with the treatment
  let foundTooth = null;
  let foundToothNumber = null;

  Object.entries(patientTeeth).forEach(([toothNumber, tooth]) => {
    const hasTreatment = tooth.treatments.some(t => t.ctId === ctId);

    if (hasTreatment) {
      foundTooth = tooth;
      foundToothNumber = toothNumber;
    }
  });

  if (foundTooth && foundToothNumber) {
    // Remove the treatment
    const newTreatments = foundTooth.treatments.filter(t => t.ctId !== ctId);

    setPatientTeeth(prev => ({
      ...prev,
      [foundToothNumber]: {
        ...foundTooth,
        treatments: newTreatments
      }
    }));
  }
};

/**
 * Handle charted_treatment_removed message
 * @param {String} ctId - The charted treatment ID
 * @param {String} position - The tooth position
 * @param {Object} teethContext - The teeth context object
 */
const handleChartedTreatmentRemoved = (ctId, position, teethContext) => {
  const { patientTeeth, setPatientTeeth } = teethContext;

  // Find the tooth with the position
  let foundToothNumber = null;

  Object.entries(patientTeeth).forEach(([toothNumber, tooth]) => {
    if (tooth.position === position) {
      foundToothNumber = toothNumber;
    }
  });

  if (foundToothNumber) {
    const tooth = patientTeeth[foundToothNumber];

    // Remove the treatment
    const newTreatments = tooth.treatments.filter(t => t.ctId !== ctId);

    setPatientTeeth(prev => ({
      ...prev,
      [foundToothNumber]: {
        ...tooth,
        treatments: newTreatments
      }
    }));

    // Send confirmation message
    sendMessage('treatment_removed', { ctId, position });
  }
};

/**
 * Handle tooth_removed message
 * @param {String} position - The tooth position
 * @param {Object} teethContext - The teeth context object
 */
const handleToothRemoved = (position, teethContext) => {
  const { patientTeeth, setPatientTeeth } = teethContext;

  // Find the tooth with the position
  let foundToothNumber = null;

  Object.entries(patientTeeth).forEach(([toothNumber, tooth]) => {
    if (tooth.position === position) {
      foundToothNumber = toothNumber;
    }
  });

  if (foundToothNumber) {
    const tooth = patientTeeth[foundToothNumber];

    setPatientTeeth(prev => ({
      ...prev,
      [foundToothNumber]: {
        ...tooth,
        marked_as_missing: true,
        treatments: [] // Clear all treatments
      }
    }));
  }
};

/**
 * Handle mixed_dentition_saved message
 * @param {String} position - The tooth position
 * @param {Boolean} active - Whether mixed dentition is active
 * @param {Object} teethContext - The teeth context object
 */
const handleMixedDentitionSaved = (position, active, teethContext) => {
  const { patientTeeth, setPatientTeeth } = teethContext;

  // Find the tooth with the position
  let foundToothNumber = null;

  Object.entries(patientTeeth).forEach(([toothNumber, tooth]) => {
    if (tooth.position === position) {
      foundToothNumber = toothNumber;
    }
  });

  if (foundToothNumber) {
    const tooth = patientTeeth[foundToothNumber];

    // Update the mixed_dentition property
    setPatientTeeth(prev => ({
      ...prev,
      [foundToothNumber]: {
        ...tooth,
        mixed_dentition: active
      }
    }));
  }
};

/**
 * Handle bridge_treatment_saved message
 */
const handleBridgeTreatmentSaved = () => {

  // This would be handled by the bridge treatment selection UI
  // which would have already set up the bridge treatment in the teeth state
  // We just need to update the ctId for all teeth in the bridge

  // This would be implemented based on how bridge treatments are stored in the state
};

/**
 * Handle charted_treatment_completed message
 * @param {String} ctId - The charted treatment ID
 * @param {String} position - The tooth position
 * @param {Object} teethContext - The teeth context object
 */
const handleChartedTreatmentCompleted = (ctId, position, teethContext) => {
  const { patientTeeth, setPatientTeeth } = teethContext;

  // Find the tooth with the position
  let foundToothNumber = null;

  Object.entries(patientTeeth).forEach(([toothNumber, tooth]) => {
    if (tooth.position === position) {
      foundToothNumber = toothNumber;
    }
  });

  if (foundToothNumber) {
    const tooth = patientTeeth[foundToothNumber];

    // Update the treatment to mark it as completed
    const newTreatments = tooth.treatments.map(t => {
      if (t.ctId === ctId) {
        return {
          ...t,
          completed: true,
          completed_at: new Date().toISOString()
        };
      }
      return t;
    });

    setPatientTeeth(prev => ({
      ...prev,
      [foundToothNumber]: {
        ...tooth,
        treatments: newTreatments
      }
    }));

    // Send confirmation message
    sendMessage('charted_treatment_completed', { ctId, position });
  }
};

/**
 * Handle initialize_single_tooth message
 * @param {Object} tooth - The tooth object
 * @param {Object} teethContext - The teeth context object
 */
const handleInitializeSingleTooth = (tooth, teethContext) => {
  const { setPatientTeeth } = teethContext;

  // Extract position number from position name (e.g., "UL8" -> "8")
  const positionNumber = tooth.position_number || extractPositionNumber(tooth.position);

  if (positionNumber) {
    // Ensure treatments have all required fields
    const processedTreatments = (tooth.treatments || []).map(treatment => ({
      Id: treatment.Id || treatment.id || Math.floor(Math.random() * 10000).toString(),
      ctid: treatment.ctid || treatment.ctId || "",
      completed: treatment.completed || false,
      name: treatment.name || "Default",
      full_tooth_treatment: treatment.full_tooth_treatment || false,
      patient_treatment: treatment.patient_treatment || false,
      remove_tooth_when_completed: treatment.remove_tooth_when_completed || false,
      remove_treatment_when_completed: treatment.remove_treatment_when_completed || false,
      bridge_treatment: treatment.bridge_treatment || false,
      missing_tooth_indicator: treatment.missing_tooth_indicator || false,
      mixed_dentition: treatment.mixed_dentition || false,
      created_at: treatment.created_at || new Date().toISOString(),
      completed_at: treatment.completed_at || null,
      surfaces: treatment.surfaces || {}
    }));

    setPatientTeeth(prev => ({
      ...prev,
      patientType: tooth.patientType || prev.patientType || "ADULT", // Preserve or set patient type
      [positionNumber]: {
        position: tooth.position,
        position_number: parseInt(positionNumber, 10),
        status: tooth.status || 'healthy',
        lastTreatment: tooth.lastTreatment || new Date().toISOString().split('T')[0],
        notes: tooth.notes || '',
        marked_as_missing: tooth.marked_as_missing || false,
        marked_as_watched: tooth.marked_as_watched || false,
        treatments: processedTreatments
      }
    }));

    // Send confirmation message
    sendMessage('single_tooth_initialized');
  }
};

/**
 * Handle watch_tooth message
 * @param {String} position - The tooth position
 * @param {Boolean} watched - Whether the tooth is watched
 * @param {Object} teethContext - The teeth context object
 */
const handleWatchTooth = (position, watched, teethContext) => {
  const { patientTeeth, setPatientTeeth } = teethContext;

  // Find the tooth with the position
  let foundToothNumber = null;

  Object.entries(patientTeeth).forEach(([toothNumber, tooth]) => {
    if (tooth.position === position) {
      foundToothNumber = toothNumber;
    }
  });

  if (foundToothNumber) {
    const tooth = patientTeeth[foundToothNumber];

    // Update the watched property
    setPatientTeeth(prev => ({
      ...prev,
      [foundToothNumber]: {
        ...tooth,
        watched
      }
    }));
  }
};

// Outgoing message functions

/**
 * Send teeth_initialized message
 * @returns {Object} - The response object
 */
export const sendTeethInitialized = () => {
  return sendMessage('teeth_initialized');
};

/**
 * Send teeth_cleared message
 * @returns {Object} - The response object
 */
export const sendTeethCleared = () => {
  return sendMessage('teeth_cleared');
};

/**
 * Send position_and_surfaces_selected message
 * @param {String} position - The tooth position
 * @param {Array} surfaces - Array of selected surfaces
 * @returns {Object} - The response object
 */
export const sendPositionAndSurfacesSelected = (position, surfaces) => {
  return sendMessage('position_and_surfaces_selected', { position, surfaces });
};

/**
 * Send charted_treatment_removed message
 * @param {String} ctId - The charted treatment ID
 * @returns {Object} - The response object
 */
export const sendChartedTreatmentRemoved = (ctId) => {
  return sendMessage('charted_treatment_removed', { ctId });
};

/**
 * Send treatment_removed message
 * @param {String} position - The tooth position
 * @param {String} ctId - The charted treatment ID
 * @returns {Object} - The response object
 */
export const sendTreatmentRemoved = (position, ctId) => {
  return sendMessage('treatment_removed', { position, ctId });
};

/**
 * Send remove_tooth message
 * @param {String} position - The tooth position
 * @returns {Object} - The response object
 */
export const sendRemoveTooth = (position) => {
  return sendMessage('remove_tooth', { position });
};

/**
 * Send mixed_dentition message
 * @param {Boolean} active - Whether mixed dentition is active
 * @param {String} position - The tooth position
 * @returns {Object} - The response object
 */
export const sendMixedDentition = (active, position) => {
  return sendMessage('mixed_dentition', { active, position });
};

/**
 * Send bridge_treatment_positions_selected message
 * @param {String} startPosition - The start position
 * @param {String} endPosition - The end position
 * @returns {Object} - The response object
 */
export const sendBridgeTreatmentPositionsSelected = (startPosition, endPosition) => {
  return sendMessage('bridge_treatment_positions_selected', { start_position: startPosition, end_position: endPosition });
};

/**
 * Send treatment_selected message
 * @param {Object} treatment - The treatment object
 * @returns {Object} - The response object
 */
export const sendTreatmentSelected = (treatment) => {
  return sendMessage('treatment_selected', { treatment });
};

/**
 * Send add_annotations message
 * @param {String} note - The note text
 * @param {String} ctId - The charted treatment ID
 * @returns {Object} - The response object
 */
export const sendAddAnnotations = (note, ctId) => {
  return sendMessage('add_annotations', { note, ctId });
};

/**
 * Send view_tooth_history message
 * @param {String} position - The tooth position
 * @returns {Object} - The response object
 */
export const sendViewToothHistory = (position) => {
  return sendMessage('view_tooth_history', { position });
};

/**
 * Send tooth_rendered message
 * @returns {Object} - The response object
 */
export const sendToothRendered = () => {
  return sendMessage('tooth_rendered');
};

/**
 * Send charted_treatment_completed message
 * @param {String} ctId - The charted treatment ID
 * @param {String} position - The tooth position
 * @returns {Object} - The response object
 */
export const sendChartedTreatmentCompleted = (ctId, position) => {
  return sendMessage('charted_treatment_completed', { ctId, position });
};

/**
 * Send single_tooth_initialized message
 * @returns {Object} - The response object
 */
export const sendSingleToothInitialized = () => {
  return sendMessage('single_tooth_initialized');
};

/**
 * Send watch_tooth message
 * @param {String} position - The tooth position
 * @param {Boolean} watched - Whether the tooth is watched
 * @returns {Object} - The response object
 */
export const sendWatchTooth = (position, watched) => {
  return sendMessage('watch_tooth', { position, watched });
};

// Helper functions

/**
 * Extract position number from position name
 * @param {String} position - The position name (e.g., "UL8")
 * @returns {String|null} - The position number or null if not found
 */
const extractPositionNumber = (position) => {
  if (!position) return null;

  // Extract the number part from the position (e.g., "UL8" -> "8")
  const match = position.match(/\d+/);

  return match ? match[0] : null;
};
