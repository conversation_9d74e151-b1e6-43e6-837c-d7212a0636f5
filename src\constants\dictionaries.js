export const TREATMENT_IDS = {
  "122d7a9f": {
    "input_treatment": "Abcess",
    "output_treatment": "Abcess"
  },
  "a76e7a94": {
    "input_treatment": "Apictomy",
    "output_treatment": "Apictomy"
  },
  "72eb9e5f": {
    "input_treatment": "BoneGraft",
    "output_treatment": "BoneGraft"
  },
  "73021766": {
    "input_treatment": "Bridge",
    "output_treatment": "Bridge"
  },
  "6e4bb571": {
    "input_treatment": "ChromeDenture",
    "output_treatment": "ChromeDenture"
  },
  "b323954b": {
    "input_treatment": "Clasp",
    "output_treatment": "Clasp"
  },
  "1eb9bbce": {
    "input_treatment": "ClosedGap",
    "output_treatment": "ClosedGap"
  },
  "1700d645": {
    "input_treatment": "CoreBuildUp",
    "output_treatment": "CoreBuildUp"
  },
  "d33811a4": {
    "input_treatment": "Crown",
    "output_treatment": "Crown"
  },
  "4b8b608a": {
    "input_treatment": "Decay",
    "output_treatment": "Decay"
  },
  "302eabdd": {
    "input_treatment": "DriftedLeft",
    "output_treatment": "DriftedLeft"
  },
  "08913950": {
    "input_treatment": "DriftedRight",
    "output_treatment": "DriftedRight"
  },
  "fe33c8d9": {
    "input_treatment": "Externalsinuslift",
    "output_treatment": "Externalsinuslift"
  },
  "b9ff15e0": {
    "input_treatment": "Filling",
    "output_treatment": "Filling"
  },
  "a8da5196": {
    "input_treatment": "FissureSealant",
    "output_treatment": "FissureSealant"
  },
  "3d64e5ff": {
    "input_treatment": "FlexiDenture",
    "output_treatment": "FlexiDenture"
  },
  "e8d94502": {
    "input_treatment": "FracturedToothLarge",
    "output_treatment": "FracturedToothLarge"
  },
  "cfa5415c": {
    "input_treatment": "FracturedToothSmall",
    "output_treatment": "FracturedToothSmall"
  },
  "3871d445": {
    "input_treatment": "ImpactedToothCut",
    "output_treatment": "ImpactedToothCut"
  },
  "c0dc92d4": {
    "input_treatment": "ImplantBridge",
    "output_treatment": "ImplantBridge"
  },
  "76b0e4ae": {
    "input_treatment": "ImplantCrown",
    "output_treatment": "ImplantCrown"
  },
  "f0bf57ae": {
    "input_treatment": "ImplantFullDenture",
    "output_treatment": "ImplantFullDenture"
  },
  "6a46ada6": {
    "input_treatment": "ImplantPartialDenture",
    "output_treatment": "ImplantPartialDenture"
  },
  "b2d60fbb": {
    "input_treatment": "Inlay",
    "output_treatment": "Inlay"
  },
  "6956f035": {
    "input_treatment": "MarylandBridge",
    "output_treatment": "MarylandBridge"
  },
  "4e918620": {
    "input_treatment": "MarylandWing",
    "output_treatment": "MarylandWing"
  },
  "cc7f117e": {
    "input_treatment": "Onlay",
    "output_treatment": "Onlay"
  },
  "f3c9b2a1": {
    "input_treatment": "PartialDenture",
    "output_treatment": "PartialDenture",
  },
  "02a20aed": {
    "input_treatment": "PartiallyEruptedTooth",
    "output_treatment": "PartiallyEruptedTooth"
  },
  "ae7ba32f": {
    "input_treatment": "PinRetention",
    "output_treatment": "PinRetention"
  },
  "f5d68f16": {
    "input_treatment": "PorcelainBondedBridge",
    "output_treatment": "PorcelainBondedBridge"
  },
  "7d4f14ff": {
    "input_treatment": "Post&Core",
    "output_treatment": "Post&Core"
  },
  "420dab2d": {
    "input_treatment": "RetaineRoot",
    "output_treatment": "RetaineRoot"
  },
  "c643a98d": {
    "input_treatment": "RootCanalTreatment",
    "output_treatment": "RootCanalTreatment",
  },
  "1ed7a422": {
    "input_treatment": "SinusDrop",
    "output_treatment": "SinusDrop"
  },
  "6bd898a1": {
    "input_treatment": "UneruptedTooth",
    "output_treatment": "UneruptedTooth"
  },
  "e0fe19dc": {
    "input_treatment": "Veneers",
    "output_treatment": "Veneers"
  },
  "2d4f4928": {
    "input_treatment": "ZygomaticImplant",
    "output_treatment": "ZygomaticImplant"
  }
}

export const TOOTH_SURFACES = {
    'UL8': ['distal_buccal', 'mesial_buccal', 'mesial', 'mesial_palatal', 'distal_palatal', 'distal', 'distal_occlusal', 'mesial_occlusal'],
    'UL7': ['distal_buccal', 'mesial_buccal', 'mesial', 'mesial_palatal', 'distal_palatal', 'distal', 'distal_occlusal', 'mesial_occlusal'],
    'UL6': ['distal_buccal', 'mesial_buccal', 'mesial', 'mesial_palatal', 'distal_palatal', 'distal', 'distal_occlusal', 'mesial_occlusal'],
    'UL5': ['buccal', 'mesial', 'palatal', 'distal', 'occlusal'],
    'UL4': ['buccal', 'mesial', 'palatal', 'distal', 'occlusal'],
    'UL3': ['buccal', 'mesial', 'palatal', 'distal', 'incisal'],
    'UL2': ['buccal', 'mesial', 'palatal', 'distal', 'incisal'],
    'UL1': ['buccal', 'mesial', 'palatal', 'distal', 'incisal'],

    'UR1': ['buccal', 'distal', 'palatal', 'mesial', 'incisal'],
    'UR2': ['buccal', 'distal', 'palatal', 'mesial', 'incisal'],
    'UR3': ['buccal', 'distal', 'palatal', 'mesial', 'incisal'],
    'UR4': ['buccal', 'distal', 'palatal', 'mesial', 'occlusal'],
    'UR5': ['buccal', 'distal', 'palatal', 'mesial', 'occlusal'],
    'UR6': ['mesial_buccal', 'distal_buccal', 'distal', 'distal_palatal', 'mesial_palatal', 'mesial', 'mesial_occlusal', 'distal_occlusal'],
    'UR7': ['mesial_buccal', 'distal_buccal', 'distal', 'distal_palatal', 'mesial_palatal', 'mesial', 'mesial_occlusal', 'distal_occlusal'],
    'UR8': ['mesial_buccal', 'distal_buccal', 'distal', 'distal_palatal', 'mesial_palatal', 'mesial', 'mesial_occlusal', 'distal_occlusal'],

    'URE': ['buccal', 'mesial', 'palatal', 'distal', 'occlusal'],
    'URD': ['buccal', 'mesial', 'palatal', 'distal', 'occlusal'],
    'URC': ['buccal', 'mesial', 'palatal', 'distal', 'incisal'],
    'URB': ['buccal', 'mesial', 'palatal', 'distal', 'incisal'],
    'URA': ['buccal', 'mesial', 'palatal', 'distal', 'incisal'],
    'ULA': ['buccal', 'distal', 'palatal', 'mesial', 'incisal'],
    'ULB': ['buccal', 'distal', 'palatal', 'mesial', 'incisal'],
    'ULC': ['buccal', 'distal', 'palatal', 'mesial', 'incisal'],
    'ULD': ['buccal', 'distal', 'palatal', 'mesial', 'occlusal'],
    'ULE': ['buccal', 'distal', 'palatal', 'mesial', 'occlusal'],
  
    'LRE': ['lingual', 'mesial', 'buccal', 'distal', 'occlusal'],
    'LRD': ['lingual', 'mesial', 'buccal', 'distal', 'occlusal'],
    'LRC': ['lingual', 'mesial', 'buccal', 'distal', 'incisal'],
    'LRB': ['lingual', 'mesial', 'buccal', 'distal', 'incisal'],
    'LRA': ['lingual', 'mesial', 'buccal', 'distal', 'incisal'],
    'LLA': ['lingual', 'distal', 'buccal', 'mesial', 'incisal'],
    'LLB': ['lingual', 'distal', 'buccal', 'mesial', 'incisal'],
    'LLC': ['lingual', 'distal', 'buccal', 'mesial', 'incisal'],
    'LLD': ['lingual', 'distal', 'buccal', 'mesial', 'occlusal'],
    'LLE': ['lingual', 'distal', 'buccal', 'mesial', 'occlusal'],
  
    'LL8': ['distal_lingual', 'mesial_lingual', 'mesial', 'mesial_buccal', 'distal_buccal', 'distal', 'distal_occlusal', 'mesial_occlusal'],
    'LL7': ['distal_lingual', 'mesial_lingual', 'mesial', 'mesial_buccal', 'distal_buccal', 'distal', 'distal_occlusal', 'mesial_occlusal'],
    'LL6': ['distal_lingual', 'mesial_lingual', 'mesial', 'mesial_buccal', 'distal_buccal', 'distal', 'distal_occlusal', 'mesial_occlusal'],
    'LL5': ['lingual', 'mesial', 'buccal', 'distal', 'occlusal'],
    'LL4': ['lingual', 'mesial', 'buccal', 'distal', 'occlusal'],
    'LL3': ['lingual', 'mesial', 'buccal', 'distal', 'incisal'],
    'LL2': ['lingual', 'mesial', 'buccal', 'distal', 'incisal'],
    'LL1': ['lingual', 'mesial', 'buccal', 'distal', 'incisal'],
    
    'LR1': ['lingual', 'distal', 'buccal', 'mesial', 'incisal'],
    'LR2': ['lingual', 'distal', 'buccal', 'mesial', 'incisal'],
    'LR3': ['lingual', 'distal', 'buccal', 'mesial', 'incisal'],
    'LR4': ['lingual', 'distal', 'buccal', 'mesial', 'occlusal'],
    'LR5': ['lingual', 'distal', 'buccal', 'mesial', 'occlusal'],
    'LR6': ['mesial_lingual', 'distal_lingual', 'distal', 'distal_buccal', 'mesial_buccal', 'mesial', 'mesial_occlusal', 'distal_occlusal'],
    'LR7': ['mesial_lingual', 'distal_lingual', 'distal', 'distal_buccal', 'mesial_buccal', 'mesial', 'mesial_occlusal', 'distal_occlusal'],
    'LR8': ['mesial_lingual', 'distal_lingual', 'distal', 'distal_buccal', 'mesial_buccal', 'mesial', 'mesial_occlusal', 'distal_occlusal']
  }
//correct naming how they gave us 
// "DistalOcclusal": { "decaySeverity": 1, "fillingSize": 1 },
//         "Distal": { "decaySeverity": 1, "fillingSize": 1 },
//         "DistalBuccal": { "decaySeverity": 1, "fillingSize": 1 },
//         "DistalPalatal": { "decaySeverity": 1, "fillingSize": 1 },
//         "Mesial": { "decaySeverity": 1, "fillingSize": 1 },
//         "MesialBuccal": { "decaySeverity": 1, "fillingSize": 1 },
//         "MesialOcclusal": { "decaySeverity": 1, "fillingSize": 1 },
//         "MesialPalatal": { "decaySeverity": 1, "fillingSize": 1 }